"""
New MCC Analysis Service implementing the simplified flow
"""

import asyncio
import json
from typing import Dict, List, Optional
from sqlmodel import Session, select
from app.database import engine
from app.models.db_models import MccAnalysis, ScrapeRequestTracker, get_current_time
from app.models.request_models import MccAnalysisRequest
from app.services.url_processor import NewUrlProcessor
from app.utils.logger import <PERSON>sol<PERSON><PERSON>ogger
from app.gpt_models.gpt_prompts import GptPromptPicker
from app.gpt_models.gemini_model_wrapper.gemeni_utils import get_gemini_response_mcc_analysis, GeminiResponseConfig
from app.utils.webhook_utils import send_mcc_results_webhook
import pandas as pd
import os


class NewMccAnalysisService:
    def __init__(self, scrape_request_ref_id: str, org_id: str = "default"):
        self.scrape_request_ref_id = scrape_request_ref_id
        self.org_id = org_id
        self.logger = ConsoleLogger(f"new_mcc_analysis_{scrape_request_ref_id}")
        self.url_processor = NewUrlProcessor(self.logger)
        
    async def __aenter__(self):
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        pass
    
    async def process_mcc_analysis(self, request_data: Optional[MccAnalysisRequest] = None) -> Dict:
        """
        Main method to process complete MCC analysis flow using the new simplified approach
        """
        try:
            self.logger.info("Starting new MCC analysis process")
            
            # Get request data from database if not provided
            if not request_data:
                request_data = await self._get_request_data_from_db()
                if not request_data:
                    return {"status": "error", "message": "No request data found"}
            
            # Create initial database record
            analysis_record = await self._create_analysis_record(request_data)
            
            # Step 1-3: Extract, dedupe, and sort URLs
            all_urls = self.url_processor.extract_all_urls(request_data.parsed_urls)
            deduped_urls = self.url_processor.dedupe_urls(all_urls)
            sorted_limited_urls = self.url_processor.sort_and_limit_urls(deduped_urls, limit=100)
            
            # Step 4-5: Find reachable URLs
            reachable_urls = await self.url_processor.find_reachable_urls(sorted_limited_urls, target_count=4)
            
            # Step 6: Get home page URL
            home_page_url = await self.url_processor.get_home_page_url(request_data.website, all_urls)
            
            # Step 7: Create final URL list
            final_urls = self.url_processor.create_final_url_list(reachable_urls, home_page_url)
            
            # Step 8: Process each URL individually (max 5 calls)
            analysis_results = await self._analyze_urls_individually(final_urls, request_data.website)
            
            # Perform MCC classification based on collected data
            mcc_result = await self._classify_mcc(analysis_results, request_data.website)
            
            # Update database record with results
            await self._update_analysis_record(analysis_record.id, mcc_result, "COMPLETED")
            
            # Send webhook if configured
            await self._send_webhook(mcc_result, request_data)
            
            self.logger.info(f"MCC analysis completed for website: {request_data.website}")
            
            return {
                "status": "success",
                "mcc_result": mcc_result,
                "analysis_id": analysis_record.id,
                "urls_analyzed": len(final_urls)
            }
            
        except Exception as e:
            self.logger.error(f"Error in MCC analysis process: {str(e)}")
            
            # Update database record with error
            if 'analysis_record' in locals():
                await self._update_analysis_record(analysis_record.id, {}, "FAILED", str(e))
            
            return {
                "status": "error",
                "message": str(e)
            }
    
    async def _get_request_data_from_db(self) -> Optional[MccAnalysisRequest]:
        """Get request data from database using scrape_request_ref_id"""
        try:
            with Session(engine) as session:
                # Get tracker record
                tracker = session.exec(
                    select(ScrapeRequestTracker).where(
                        ScrapeRequestTracker.scrape_request_ref_id == self.scrape_request_ref_id
                    )
                ).first()

                if not tracker:
                    self.logger.error(f"No tracker found for scrape_request_ref_id: {self.scrape_request_ref_id}")
                    return None

                # Get actual URL data from WebsiteUrls table
                from app.models.db_models import WebsiteUrls
                website_urls = session.exec(
                    select(WebsiteUrls).where(
                        WebsiteUrls.scrape_request_ref_id == self.scrape_request_ref_id
                    )
                ).all()

                # Organize URLs by depth
                depth_1_urls = []
                depth_2_urls = []

                for url_record in website_urls:
                    if url_record.depth == 1:
                        depth_1_urls.append(url_record.url)
                    elif url_record.depth == 2:
                        depth_2_urls.append(url_record.url)

                self.logger.info(f"Retrieved {len(depth_1_urls)} depth-1 URLs and {len(depth_2_urls)} depth-2 URLs")

                return MccAnalysisRequest(
                    website=tracker.website,
                    scrapeRequestRefID=self.scrape_request_ref_id,
                    org_id=self.org_id,
                    parsed_urls={"depth_1": depth_1_urls, "depth_2": depth_2_urls}
                )

        except Exception as e:
            self.logger.error(f"Error getting request data from DB: {str(e)}")
            return None
    
    async def _create_analysis_record(self, request: MccAnalysisRequest) -> MccAnalysis:
        """Create initial MCC analysis record in database"""
        try:
            with Session(engine) as session:
                analysis = MccAnalysis(
                    scrape_request_ref_id=request.scrapeRequestRefID,
                    website=request.website,
                    org_id=request.org_id,
                    status="PROCESSING",
                    created_at=get_current_time()
                )
                
                session.add(analysis)
                session.commit()
                session.refresh(analysis)
                
                self.logger.info(f"Created MCC analysis record with ID: {analysis.id}")
                return analysis
                
        except Exception as e:
            self.logger.error(f"Error creating analysis record: {str(e)}")
            raise
    
    async def _analyze_urls_individually(self, urls: List[str], website: str) -> List[Dict]:
        """Analyze each URL individually and collect results"""
        results = []
        
        for i, url in enumerate(urls, 1):
            self.logger.info(f"Analyzing URL {i}/{len(urls)}: {url}")
            
            try:
                result = await self.url_processor.analyze_url_individually(website, url)
                if result:
                    result['url'] = url
                    results.append(result)
                    
            except Exception as e:
                self.logger.error(f"Error analyzing URL {url}: {str(e)}")
                
        self.logger.info(f"Completed analysis of {len(results)} URLs")
        return results
    
    async def _classify_mcc(self, analysis_results: List[Dict], website: str) -> Dict:
        """Classify MCC based on collected website information"""
        try:
            # Combine information from all analyzed URLs
            combined_info = self._combine_analysis_results(analysis_results)
            
            # Load MCC data for classification
            mcc_data = self._load_mcc_data()
            
            # Generate MCC classification prompt
            prompt = GptPromptPicker.get_website_mcc_classifier_prompt_full(
                website_info=combined_info,
                filtered_special_mcc=mcc_data['special_mcc'],
                filtered_visa_data=mcc_data['visa_mcc'],
                website_url=website
            )
            
            # Get MCC classification
            config = GeminiResponseConfig(
                model_name="gemini-1.5-flash",
                task_type="mcc_classification",
                max_retries=3
            )
            
            response = await get_gemini_response_mcc_analysis(
                prompt=prompt,
                config=config,
                request_id=f"mcc_classification_{website}"
            )
            
            if response:
                self.logger.info(f"MCC classification completed: {response.get('mcc', 'Unknown')}")
                return response
            else:
                self.logger.error("No response from MCC classification")
                return {"mcc": "-1", "error": "Classification failed"}
                
        except Exception as e:
            self.logger.error(f"Error in MCC classification: {str(e)}")
            return {"mcc": "-1", "error": str(e)}
    
    def _combine_analysis_results(self, results: List[Dict]) -> Dict:
        """Combine analysis results from multiple URLs into a single summary"""
        combined = {
            "product_services": "",
            "line_of_business": "",
            "customers": "",
            "website_description": ""
        }
        
        try:
            # Collect all information
            all_products = []
            all_business = []
            all_customers = []
            all_descriptions = []
            
            for result in results:
                if result.get('product_services'):
                    all_products.append(result['product_services'])
                if result.get('line_of_business'):
                    all_business.append(result['line_of_business'])
                if result.get('customers'):
                    all_customers.append(result['customers'])
                if result.get('website_description'):
                    all_descriptions.append(result['website_description'])
            
            # Combine and deduplicate
            combined['product_services'] = "; ".join(set(all_products))
            combined['line_of_business'] = "; ".join(set(all_business))
            combined['customers'] = "; ".join(set(all_customers))
            combined['website_description'] = "; ".join(set(all_descriptions))
            
            # Clean up combined information
            for key in combined:
                combined[key] = combined[key].strip()
            
            return combined
            
        except Exception as e:
            self.logger.error(f"Error combining analysis results: {str(e)}")
            return combined
    
    def _load_mcc_data(self) -> Dict:
        """Load MCC data for classification"""
        try:
            # Load MCC data from CSV files (similar to old service)
            special_mcc = []
            visa_mcc = []

            # Try to load special MCC data
            try:
                special_mcc_path = "app/data/special_mcc.csv"
                if os.path.exists(special_mcc_path):
                    special_df = pd.read_csv(special_mcc_path)
                    special_mcc = special_df.to_dict('records')
                    self.logger.info(f"Loaded {len(special_mcc)} special MCC records")
            except Exception as e:
                self.logger.warning(f"Could not load special MCC data: {str(e)}")

            # Try to load Visa MCC data
            try:
                visa_mcc_path = "app/data/visa_mcc.csv"
                if os.path.exists(visa_mcc_path):
                    visa_df = pd.read_csv(visa_mcc_path)
                    visa_mcc = visa_df.to_dict('records')
                    self.logger.info(f"Loaded {len(visa_mcc)} Visa MCC records")
            except Exception as e:
                self.logger.warning(f"Could not load Visa MCC data: {str(e)}")

            return {
                "special_mcc": special_mcc,
                "visa_mcc": visa_mcc
            }

        except Exception as e:
            self.logger.error(f"Error loading MCC data: {str(e)}")
            return {"special_mcc": [], "visa_mcc": []}
    
    async def _update_analysis_record(self, analysis_id: int, mcc_result: Dict, status: str, error_message: str = None):
        """Update MCC analysis record with results"""
        try:
            with Session(engine) as session:
                analysis = session.get(MccAnalysis, analysis_id)
                if analysis:
                    analysis.status = status
                    analysis.completed_at = get_current_time()
                    
                    if mcc_result:
                        analysis.mcc_code = mcc_result.get('mcc', '-1')
                        analysis.business_description = mcc_result.get('business_desc', '')
                        analysis.business_category = mcc_result.get('business_category', '')
                        analysis.classification_reason = mcc_result.get('reason', '')
                        analysis.raw_response = json.dumps(mcc_result)
                    
                    if error_message:
                        analysis.error_message = error_message
                    
                    session.commit()
                    self.logger.info(f"Updated analysis record {analysis_id} with status: {status}")
                    
        except Exception as e:
            self.logger.error(f"Error updating analysis record: {str(e)}")
    
    async def _send_webhook(self, mcc_result: Dict, request_data: MccAnalysisRequest):
        """Send webhook with MCC results"""
        try:
            webhook_data = {
                "scrape_request_ref_id": request_data.scrapeRequestRefID,
                "website": request_data.website,
                "mcc_result": mcc_result,
                "org_id": request_data.org_id
            }
            
            await send_mcc_results_webhook(webhook_data)
            self.logger.info("Webhook sent successfully")
            
        except Exception as e:
            self.logger.error(f"Error sending webhook: {str(e)}")
