from app.gpt_models.gpt_prompts import GptPromptPicker
from app.gpt_models.chatgpt_utils import <PERSON>TLogger
from app.utils.logger import ConsoleLogger
from app.database import get_session, get_async_session, AsyncSessionLocal
from typing import Optional, List, Tuple, Dict, Any
import json
import time
import traceback
from datetime import datetime
import os
import asyncio
import pandas as pd

from app.services.url_classification import get_urls_by_depth
from app.services.url_classification import urlclassification_service 
from app.models.request_models import MccAnalysisRequest
from sqlmodel import select
from sqlalchemy.ext.asyncio import AsyncSession
from app.models.db_models import (
    WebsiteUrls, MccAnalysis, MccUrlClassification, ScrapeRequestTracker, get_current_time, engine
)
from app.gpt_models.gemini_model_wrapper.gemeni_utils import get_gemini_response_mcc_analysis, GeminiResponseConfig
from app.utils.website_url_processor import get_urls_by_scrape_ref
from app.utils.webhook_utils import send_mcc_results_webhook


"""
mcc service flow this will be a celery task:
- request comes in the form of the flow descbed in @models/request_models.py
- we will first check if the url exists or not by checking the database named website_urls using scrape request ref id
- using url classification service to classify the urls into required categories and subcategories using soft classification and hard classification
- after classifying the urls, we will use the classified urls for mcc identification of the website
- priority usrls to use are home page, about page, category pages, product pages.
- we will make a gemeni call the prompt is in @gpt_models/gpt_prompts.py
- we will then use the results from the gemeni call to identify the mcc of the website present in @gemini_model_wrapper/gemini_client.py
- we will be making two calls one to get the informatin of the site (function  name: get_website_information_prompt) and
    other to get the mcc information of the site (function name: get_website_mcc_classifier_prompt)
- after that we will clear the json data and get clear outputs and store it in database named mcc_analysis

things to keep in mind:
- loggers has to be at every step for detailed logging and debugging
- if there is an error while classifying the mcc, we have to retry 3 times and gracefully handle the error
"""


class MccClassificationService:
    """
    MCC Classification Service - handles complete MCC analysis flow
    """
    
    def __init__(self, scrape_request_ref_id: str, org_id: str = "default"):
        self.scrape_request_ref_id = scrape_request_ref_id
        self.org_id = org_id
        self.logger = ConsoleLogger(analysis_id=scrape_request_ref_id)

        # We'll create async sessions as needed instead of keeping one persistent session
        self.db_session = None  # Will be set to async session when needed

        # Priority categories for MCC analysis
        self.priority_categories = {
            "home_page", "about_us", "catalogue", "products"
        }

        # Maximum number of URLs to use for MCC analysis
        self.max_urls_for_mcc = 18

    async def get_async_session(self) -> AsyncSession:
        """Get async database session"""
        return AsyncSessionLocal()

    async def __aenter__(self):
        """Async context manager entry"""
        self.db_session = await self.get_async_session()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.db_session:
            await self.db_session.close()
    
    async def check_urls_exist_in_db(self) -> Tuple[bool, Optional[Dict]]:
        """
        Check if URLs exist in database using scrape request ref id
        
        Returns:
            Tuple[bool, Optional[Dict]]: (exists, urls_data)
        """
        self.logger.info(
            "Checking if URLs exist in database", 
            {"scrape_request_ref_id": self.scrape_request_ref_id}
        )
        
        try:
            # Create async session for database operations
            async with AsyncSessionLocal() as session:
                # Get URLs by scrape request ref ID (using async session)
                from sqlmodel import select
                from app.models.db_models import WebsiteUrls, MccAnalysis

                # Get URLs directly from database
                result = await session.execute(
                    select(WebsiteUrls).where(
                        WebsiteUrls.scrape_request_ref_id == self.scrape_request_ref_id
                    )
                )
                urls_records = result.scalars().all()

                if not urls_records:
                    self.logger.warning(
                        "No URLs found for scrape request",
                        {"scrape_request_ref_id": self.scrape_request_ref_id}
                    )
                    return False, None

                # Convert to dict format
                urls = [
                    {
                        "url": url.url,
                        "soft_class": url.soft_class,
                        "priority_url": url.priority_url,
                        "depth": url.depth
                    }
                    for url in urls_records
                ]

                # Get the MccAnalysis record to find the website URL
                result = await session.execute(
                    select(MccAnalysis).where(
                        MccAnalysis.scrape_request_ref_id == self.scrape_request_ref_id
                    )
                )
                mcc_analysis = result.scalar_one_or_none()
            
            if not mcc_analysis:
                self.logger.warning(
                    "MCC analysis record not found",
                    {"scrape_request_ref_id": self.scrape_request_ref_id}
                )
                return False, None
            
            website = mcc_analysis.website
            
            # Organize URLs by depth
            urls_data = {
                "website": website,
                "parsed_urls": []
            }
            
            urls_by_depth = {}
            for url_info in urls:
                depth = url_info.get('depth', 1)
                if depth not in urls_by_depth:
                    urls_by_depth[depth] = []
                urls_by_depth[depth].append(url_info['url'])
            
            # Convert to expected format
            for depth, urls_list in urls_by_depth.items():
                urls_data["parsed_urls"].append({
                    "url_depth": depth,
                    "urls": urls_list
                })
            
            self.logger.info(
                "URLs found in database",
                {
                    "website": website,
                    "total_depths": len(urls_by_depth),
                    "total_urls": len(urls)
                }
            )
            
            return True, urls_data
            
        except Exception as e:
            self.logger.error("Error checking URLs in database", error=e)
            return False, None

    async def prepare_data_for_analysis(self, website: str, urls_data: Dict) -> Tuple[Optional[Dict], Optional[Dict], Optional[Dict]]:
        """
        Prepare data for MCC analysis by classifying URLs with comprehensive error handling

        Args:
            website (str): Website URL
            urls_data (Dict): URLs data organized by depth

        Returns:
            Tuple[Optional[Dict], Optional[Dict], Optional[Dict]]: (classified_urls, priority_urls, soft_classified_urls)
        """
        self.logger.info(
            "Preparing data for MCC analysis",
            {"website": website}
        )
        
        # Default return values
        default_classified_urls = {"home_page": [website], "about_us": [], "catalogue": []}
        default_priority_urls = {"home_page": [website]}
        
        try:
            # Validate inputs
            if not website or not isinstance(website, str):
                self.logger.error("Invalid website URL provided", {"website": website, "type": type(website)})
                return default_classified_urls, default_priority_urls, {}
            
            if not urls_data or not isinstance(urls_data, dict):
                self.logger.error("Invalid URLs data provided", {"urls_data_type": type(urls_data)})
                return default_classified_urls, default_priority_urls, {}
            
            # No longer need to get website_id - using scrape_request_ref_id directly
            
            # Extract URLs by depth with error handling
            urls_depth_1 = []
            urls_depth_2 = []
            
            try:
                parsed_urls = urls_data.get("parsed_urls", [])
                if not isinstance(parsed_urls, list):
                    self.logger.warning("parsed_urls is not a list, using empty list", {"parsed_urls_type": type(parsed_urls)})
                    parsed_urls = []
                
                for parsed_url_obj in parsed_urls:
                    if not isinstance(parsed_url_obj, dict):
                        self.logger.warning("Skipping invalid parsed_url_obj", {"obj_type": type(parsed_url_obj)})
                        continue
                    
                    url_depth = parsed_url_obj.get("url_depth")
                    urls = parsed_url_obj.get("urls", [])
                    
                    if not isinstance(urls, list):
                        self.logger.warning("URLs is not a list, skipping", {"urls_type": type(urls), "depth": url_depth})
                        continue
                    
                    if url_depth == 1:
                        urls_depth_1.extend(urls)
                    elif url_depth == 2:
                        urls_depth_2.extend(urls)
                        
            except Exception as url_extraction_error:
                self.logger.error("Error extracting URLs by depth", {"error": str(url_extraction_error)})
                # Use website as fallback for depth 1
                urls_depth_1 = [website]
                urls_depth_2 = []
            
            self.logger.info(
                "Got URLs by depth",
                {"depth_1_count": len(urls_depth_1), "depth_2_count": len(urls_depth_2), "scrape_request_ref_id": self.scrape_request_ref_id}
            )
            
            # NEW: First check if classification results already exist in database from URL classification task
            try:
                self.logger.info("Checking for existing classification results in database")
                
                # Get stored classification results from WebsiteUrls table
                from sqlmodel import select
                from app.models.db_models import WebsiteUrls
                
                result = await self.db_session.execute(
                    select(WebsiteUrls).where(
                        WebsiteUrls.scrape_request_ref_id == self.scrape_request_ref_id
                    )
                )
                stored_urls = result.scalars().all()
                
                if stored_urls:
                    self.logger.info(f"Found {len(stored_urls)} stored URL records")
                    
                    # Reconstruct classification results from stored data
                    # Use dynamic dictionary to handle any category names from database
                    stored_classified_urls = {
                        "home_page": [], "about_us": [], "terms_and_condition": [],
                        "returns_cancellation_exchange": [], "privacy_policy": [],
                        "shipping_delivery": [], "contact_us": [], "catalogue": [],
                        "products": [], "services": [], "contact_page": [], 
                        "pinterest_page": [], "instagram_page": [], "facebook_page": [],
                        "youtube_page": [], "linkedin_page": [], "twitter_page": [],
                        "urls_not_reachable": []
                    }
                    
                    urls_with_empty_classification = []
                    total_stored_urls = len(stored_urls)
                    
                    for url_record in stored_urls:
                        url = url_record.url
                        soft_class_str = url_record.soft_class
                        hard_class_str = url_record.hard_class
                        
                        try:
                            # Prefer hard classification over soft classification when available
                            classification_to_use = None
                            classification_type = "none"
                            
                            # Check for hard classification first
                            if hard_class_str and hard_class_str.strip():
                                try:
                                    import json
                                    hard_class = json.loads(hard_class_str)
                                    if isinstance(hard_class, list) and hard_class:
                                        classification_to_use = hard_class
                                        classification_type = "hard"
                                    elif isinstance(hard_class, str) and hard_class.strip():
                                        classification_to_use = [hard_class.strip()]
                                        classification_type = "hard"
                                except (json.JSONDecodeError, Exception) as hard_parse_error:
                                    self.logger.warning(f"Error parsing hard classification for {url}: {hard_parse_error}")
                            
                            # Fall back to soft classification if hard classification is not available
                            if not classification_to_use and soft_class_str and soft_class_str.strip():
                                try:
                                    import json
                                    soft_class = json.loads(soft_class_str)
                                    if isinstance(soft_class, list) and soft_class:
                                        classification_to_use = soft_class
                                        classification_type = "soft"
                                    elif isinstance(soft_class, str) and soft_class.strip():
                                        classification_to_use = [soft_class.strip()]
                                        classification_type = "soft"
                                except (json.JSONDecodeError, Exception) as soft_parse_error:
                                    self.logger.warning(f"Error parsing soft classification for {url}: {soft_parse_error}")
                            
                            if classification_to_use:
                                # URL has classifications
                                for category in classification_to_use:
                                    if category in stored_classified_urls:
                                        stored_classified_urls[category].append(url)
                                    else:
                                        # Handle unknown category by creating it dynamically
                                        if category and isinstance(category, str):
                                            if category not in stored_classified_urls:
                                                stored_classified_urls[category] = []
                                            stored_classified_urls[category].append(url)
                                            self.logger.debug(f"Added URL to dynamic category: {category} (from {classification_type} classification)")
                            else:
                                # No classification available
                                urls_with_empty_classification.append(url)
                                
                        except Exception as parse_error:
                            self.logger.warning(f"Error processing stored classification for {url}: {parse_error}")
                            urls_with_empty_classification.append(url)
                    
                    # Add URLs with no classification to unreachable (safely with error handling)
                    if "urls_not_reachable" not in stored_classified_urls:
                        stored_classified_urls["urls_not_reachable"] = []
                    
                    if isinstance(stored_classified_urls["urls_not_reachable"], list):
                        stored_classified_urls["urls_not_reachable"].extend(urls_with_empty_classification)
                    else:
                        # Fix invalid structure
                        stored_classified_urls["urls_not_reachable"] = urls_with_empty_classification.copy()
                    
                    # Check if URLs are marked as unreachable/unclassified - safely with error handling
                    # Fix potential KeyError and protect against invalid data types
                    try:
                        unreachable_count = len(stored_classified_urls.get("urls_not_reachable", []))
                        classified_count = sum(len(urls) for category, urls in stored_classified_urls.items() 
                                            if category != "urls_not_reachable" and isinstance(urls, list) and urls)
                    except Exception as count_error:
                        self.logger.error(f"Error calculating unreachable counts: {count_error}")
                        unreachable_count = 0
                        classified_count = 0
                    
                    self.logger.info(
                        "Stored classification analysis",
                        {
                            "total_stored_urls": total_stored_urls,
                            "unreachable_count": unreachable_count,
                            "classified_count": classified_count,
                            "stored_categories": {k: len(v) for k, v in stored_classified_urls.items() if v}
                        }
                    )
                    
                                    # [CRITICAL FIX] Check if we have good stored classification results
                if classified_count > 0:
                    self.logger.info(
                        "✅ Using stored classification results - skipping fresh API calls",
                        {
                            "total_urls": total_stored_urls,
                            "unreachable_urls": unreachable_count,
                            "classified_urls": classified_count,
                            "action": "USING_STORED_CLASSIFICATION",
                            "scrape_ref": self.scrape_request_ref_id,
                            "time": datetime.now().isoformat()
                        }
                    )
                    
                    # Filter priority URLs and return stored results
                    priority_urls = self.filter_priority_urls(stored_classified_urls, website)
                    if not priority_urls or not any(urls for urls in priority_urls.values()):
                        priority_urls = {"home_page": [website]}
                    
                    return stored_classified_urls, priority_urls, stored_classified_urls
                
                # Only force fresh classification if ALL URLs are unreachable/unclassified
                elif unreachable_count >= total_stored_urls and total_stored_urls > 0 and classified_count == 0:
                    self.logger.warning(
                        "[RACE CONDITION FIX] All stored URLs marked unreachable - forcing fresh classification",
                        {
                            "total_urls": total_stored_urls,
                            "unreachable_urls": unreachable_count,
                            "classified_urls": classified_count,
                            "action": "FORCING_FRESH_CLASSIFICATION",
                            "scrape_ref": self.scrape_request_ref_id,
                            "fix_type": "RACE_CONDITION_PROTECTION",
                            "time": datetime.now().isoformat()
                        }
                    )
                    
                    # Force fresh classification as verification step
                    # DON'T return here - continue to the fresh API calls below
                    pass
                
                self.logger.info("No usable stored classification results found, proceeding with fresh API calls")
                
            except Exception as stored_check_error:
                self.logger.warning(f"Error checking stored classification results: {stored_check_error}")
                self.logger.info("Proceeding with fresh classification API calls")
            
            # Perform soft classification with proper IDs and error handling
            try:
                self.logger.info("Starting soft classification of URLs")

                # Create URL classification service instance
                url_service = urlclassification_service(
                    website=website,
                    scrape_request_ref_id=self.scrape_request_ref_id,
                    org_id=self.org_id
                )

                # Protected tuple unpacking for soft classification
                soft_result = url_service.soft_classify_urls(
                    urls_depth_1,  # urls1
                    urls_depth_2,  # urls2
                    self.logger,   # logger
                    self.scrape_request_ref_id,  # scrape_request_ref_id
                    self.org_id    # org_id
                )
                
                if soft_result is None:
                    self.logger.error("soft_classify_urls returned None instead of tuple")
                    raise ValueError("soft_classify_urls returned None")
                
                if not isinstance(soft_result, tuple):
                    self.logger.error(f"soft_classify_urls returned {type(soft_result)} instead of tuple")
                    raise ValueError(f"Expected tuple, got {type(soft_result)}")
                
                if len(soft_result) != 2:
                    self.logger.error(f"soft_classify_urls returned tuple of length {len(soft_result)}")
                    raise ValueError(f"Expected tuple of length 2, got {len(soft_result)}")
                
                output_df, soft_classified_urls = soft_result
                
                self.logger.info("Successfully unpacked soft classification result", {
                    "output_df_type": type(output_df),
                    "soft_classified_urls_type": type(soft_classified_urls)
                })
                
                if not soft_classified_urls or not isinstance(soft_classified_urls, dict):
                    self.logger.error("Soft classification failed or returned invalid data",
                                    {"result_type": type(soft_classified_urls)})
                    return default_classified_urls, default_priority_urls, {}
                
                # Check for unreachable URLs
                unreachable_urls = soft_classified_urls.get("urls_not_reachable")
                if unreachable_urls is not None:
                    self.logger.warning("Some URLs not reachable during soft classification", 
                                      {"unreachable_count": len(unreachable_urls) if isinstance(unreachable_urls, list) else "unknown"})
                
                # Check for the case when ALL URLs are truly unreachable
                total_urls_count = len(urls_depth_1) + len(urls_depth_2)
                total_unreachable = 0
                if unreachable_urls is not None and isinstance(unreachable_urls, list):
                    total_unreachable = len(unreachable_urls)
                
                # Log the ratio of unreachable URLs for transparency
                self.logger.info(
                    "Soft classification unreachable URL analysis", 
                    {
                        "total_urls": total_urls_count,
                        "unreachable_count": total_unreachable,
                        "unreachable_ratio": f"{total_unreachable}/{total_urls_count}",
                        "unreachable_percentage": f"{(total_unreachable/total_urls_count)*100:.1f}%" if total_urls_count > 0 else "0%",
                        "urls_by_category": {k: len(v) if isinstance(v, list) else 0 for k, v in soft_classified_urls.items() if k != "urls_not_reachable"}
                    }
                )
                
                # Only truly trigger fallback if ALL URLs are unreachable AND there are no categorized URLs
                if unreachable_urls is not None and isinstance(unreachable_urls, list):
                    # Count URLs in categories other than urls_not_reachable
                    categorized_urls = sum(len(urls) for category, urls in soft_classified_urls.items() 
                                          if category != "urls_not_reachable" and isinstance(urls, list))
                    
                    if len(unreachable_urls) >= total_urls_count and total_urls_count > 0 and categorized_urls == 0:
                        self.logger.error(
                            "ALL URLs are truly unreachable with NO categorized URLs - site is not accessible", 
                            {
                                "total_urls": total_urls_count,
                                "unreachable_urls": len(unreachable_urls),
                                "categorized_urls": categorized_urls,
                                "decision": "TRIGGERING_BACKUP_FLOW"
                            }
                        )
                        # Store home page URL from soft classification for later use
                        self.home_page_url = website
                        if "home_page" in soft_classified_urls and soft_classified_urls["home_page"]:
                            self.home_page_url = soft_classified_urls["home_page"][0]
                        
                        # Return None to indicate site is unreachable and should stop processing
                        return None, None, None
                
                self.logger.info(
                    "Soft classification completed",
                    {"categories_found": list(soft_classified_urls.keys())}
                )
                
            except Exception as soft_classification_error:
                self.logger.error("Error during soft classification", {"error": str(soft_classification_error)})
                return default_classified_urls, default_priority_urls, {}
            
            # Perform hard classification with error handling
            try:
                self.logger.info("Starting hard classification of URLs")
                # Always proceed with hard classification to verify URL reachability
                # Removed restrictive condition that was skipping hard classification for unreachable sites
                hard_classified_urls = url_service.hard_classify_urls(soft_classified_urls)
                
                if not hard_classified_urls or not isinstance(hard_classified_urls, dict):
                    self.logger.warning("Hard classification failed, using soft classification results")
                    hard_classified_urls = soft_classified_urls
                
                self.logger.info(
                    "Hard classification completed",
                    {"categories_found": list(hard_classified_urls.keys())}
                )
                
                # Calculate detailed category counts for hard classification results
                hard_unreachable_urls = hard_classified_urls.get("urls_not_reachable", [])
                
                # Calculate detailed category counts for logging and decision making
                category_counts = {}
                total_reachable_urls = 0
                categorized_priority_urls = 0
                
                for category, urls in hard_classified_urls.items():
                    if isinstance(urls, list):
                        count = len(urls)
                        category_counts[category] = count
                        if category != "urls_not_reachable":
                            total_reachable_urls += count
                            # Count priority URLs specifically (home_page, about_us, catalogue)
                            if category in self.priority_categories:
                                categorized_priority_urls += count
                    else:
                        category_counts[category] = 0
                
                # Log very detailed category breakdown for debugging
                self.logger.info(
                    "Hard classification results - detailed breakdown",
                    {
                        "category_counts": category_counts,
                        "total_reachable_urls": total_reachable_urls,
                        "priority_reachable_urls": categorized_priority_urls,
                        "total_unreachable_urls": len(hard_unreachable_urls) if isinstance(hard_unreachable_urls, list) else 0,
                        "unreachable_percentage": f"{(len(hard_unreachable_urls) / (len(hard_unreachable_urls) + total_reachable_urls))*100:.1f}%" if (len(hard_unreachable_urls) + total_reachable_urls) > 0 else "0%"
                    }
                )
                
                # 🎯 BACKUP FLOW TRIGGER: Check if hard classification marked URLs as unreachable
                unreachable_via_tool = hard_classified_urls.get("Unreachable_via_tool", [])
                if total_reachable_urls == 0 and len(hard_unreachable_urls) > 0:
                    self.logger.warning(
                        "🔄 HARD CLASSIFICATION UNREACHABLE - FALLING BACK TO SOFT CLASSIFICATION",
                        {
                            "total_reachable_urls": total_reachable_urls,
                            "unreachable_urls_count": len(hard_unreachable_urls),
                            "unreachable_via_tool_count": len(unreachable_via_tool),
                            "category_breakdown": category_counts,
                            "decision": "FALLBACK_TO_SOFT_CLASSIFICATION",
                            "fix_applied": "ignore_hard_classification_use_soft_only"
                        }
                    )

                    # 🚀 FIX 1: Fallback to soft classification when hard classification fails
                    self.logger.info("✅ Using soft classification results instead of hard classification")
                    hard_classified_urls = soft_classified_urls.copy()

                    # Remove any unreachable categories from soft classification
                    if "urls_not_reachable" in hard_classified_urls:
                        del hard_classified_urls["urls_not_reachable"]
                    if "Unreachable_via_tool" in hard_classified_urls:
                        del hard_classified_urls["Unreachable_via_tool"]

                    self.logger.info("🔄 Soft classification fallback applied", {
                        "soft_categories": list(hard_classified_urls.keys()),
                        "soft_url_counts": {k: len(v) for k, v in hard_classified_urls.items() if isinstance(v, list)}
                    })
                else:
                    # Even if there are some unreachable URLs, we proceed normally as long as we have reachable ones
                    self.logger.info(
                        "Some URLs are reachable - continuing with normal flow",
                        {
                            "total_reachable_urls": total_reachable_urls,
                            "priority_urls_found": categorized_priority_urls,
                            "decision": "NORMAL_FLOW_CONTINUES",
                            "reachable_categories": [cat for cat, count in category_counts.items() if count > 0 and cat not in ["urls_not_reachable", "Unreachable_via_tool"]]
                        }
                    )

                # 🚀 Additional check: If we still have no reachable URLs after soft classification fallback
                if total_reachable_urls == 0 and hard_classified_urls == soft_classified_urls:
                    self.logger.warning(
                        "🔄 BACKUP FLOW TRIGGERED: Even soft classification shows no reachable URLs",
                        {
                            "decision": "BACKUP_FLOW_TRIGGERED",
                            "backup_method": "text_extraction_from_top_3_urls"
                        }
                    )

                    # Execute backup flow: Extract text from top 3 URLs and process
                    backup_result = await self.execute_backup_flow_with_text_extraction(
                        website, hard_unreachable_urls, soft_classified_urls
                    )

                    if backup_result:
                        self.logger.info("✅ Backup flow completed successfully - returning results")
                        return backup_result
                    else:
                        self.logger.warning("❌ Backup flow failed - falling back to site unreachable handler")
                        return None, None, None
                
            except Exception as hard_classification_error:
                self.logger.error("Error during hard classification", {"error": str(hard_classification_error)})
                hard_classified_urls = soft_classified_urls  # Fallback to soft classification
            
            # Filter priority URLs with enhanced error handling and logging
            try:
                self.logger.info("Starting priority URL filtering", {
                    "input_categories": list(hard_classified_urls.keys()),
                    "priority_categories_defined": list(self.priority_categories)
                })
                
                priority_urls = self.filter_priority_urls(hard_classified_urls, website)
                
                # Log the filtering results
                priority_url_counts = {k: len(v) for k, v in priority_urls.items() if isinstance(v, list)}
                total_priority_urls = sum(priority_url_counts.values())
                
                self.logger.info("Priority URL filtering completed", {
                    "priority_url_counts": priority_url_counts,
                    "total_priority_urls": total_priority_urls
                })
                
                # 🚀 FIX 3: Enhanced fallback to main page/landing page
                if not priority_urls or total_priority_urls == 0:
                    self.logger.warning("No priority URLs found after filtering, applying main page fallback", {
                        "fallback_website": website,
                        "original_priority_urls": priority_urls
                    })

                    # Try to use home page from soft classification first
                    fallback_url = website
                    if soft_classified_urls and "home_page" in soft_classified_urls and soft_classified_urls["home_page"]:
                        fallback_url = soft_classified_urls["home_page"][0]
                        self.logger.info(f"✅ Using home page from soft classification: {fallback_url}")
                    else:
                        self.logger.info(f"✅ Using main website URL as fallback: {fallback_url}")

                    priority_urls = {"home_page": [fallback_url]}
                
            except Exception as priority_filter_error:
                self.logger.error("Error filtering priority URLs", {
                    "error": str(priority_filter_error),
                    "error_type": type(priority_filter_error).__name__,
                    "traceback": traceback.format_exc()
                })
                priority_urls = {"home_page": [website]}
            
            self.logger.info(
                "Data preparation completed",
                {
                    "total_classified_urls": sum(len(urls) for urls in hard_classified_urls.values() if isinstance(urls, list)),
                    "priority_urls_count": sum(len(urls) for urls in priority_urls.values() if isinstance(urls, list))
                }
            )
            
            return hard_classified_urls, priority_urls, soft_classified_urls
            
        except Exception as e:
            self.logger.error("Unexpected error preparing data for analysis", {"error": str(e), "traceback": traceback.format_exc()})
            return default_classified_urls, default_priority_urls, {}
    
    def filter_priority_urls(self, classified_urls: Dict, website: str = None) -> Dict[str, List[str]]:
        """
        Filter URLs to get priority URLs for MCC analysis with comprehensive error handling

        Args:
            classified_urls (Dict): Classified URLs dictionary
            website (str): Main website URL for fallback purposes

        Returns:
            Dict[str, List[str]]: Priority URLs by category
        """
        # Default fallback
        default_priority_urls = {}
        
        try:
            # Validate input
            if not classified_urls or not isinstance(classified_urls, dict):
                self.logger.warning("Invalid classified_urls provided", {"type": type(classified_urls)})
                return default_priority_urls
            
            # Log input details for debugging
            input_category_counts = {k: len(v) if isinstance(v, list) else 0 for k, v in classified_urls.items()}
            self.logger.info(
                "Filtering priority URLs for MCC analysis - input analysis",
                {
                    "total_categories": len(classified_urls),
                    "input_category_counts": input_category_counts,
                    "priority_categories": list(self.priority_categories),
                    "max_urls_for_mcc": self.max_urls_for_mcc
                }
            )
            
            priority_urls = {}
            
            # Calculate URLs per category safely
            priority_category_count = len(self.priority_categories)
            if priority_category_count == 0:
                self.logger.warning("No priority categories defined, using default")
                urls_per_category = self.max_urls_for_mcc
            else:
                urls_per_category = self.max_urls_for_mcc
            
            self.logger.info(f"URLs per category calculated: {urls_per_category}")
            
            for category, urls in classified_urls.items():
                try:
                    # Validate category and URLs
                    if not isinstance(category, str):
                        self.logger.warning("Skipping invalid category", {"category": category, "type": type(category)})
                        continue
                    
                    if not isinstance(urls, list):
                        self.logger.warning("URLs not a list for category", {"category": category, "urls_type": type(urls)})
                        continue
                    
                    # Log category processing
                    self.logger.debug(f"Processing category '{category}': {len(urls)} URLs", {
                        "category": category,
                        "url_count": len(urls),
                        "is_priority": category in self.priority_categories,
                        "has_urls": len(urls) > 0
                    })
                    
                    # Include priority categories and their URLs
                    if category in self.priority_categories and urls:
                        # Filter valid URLs
                        valid_urls = [url for url in urls if isinstance(url, str) and url.strip()]
                        if valid_urls:
                            priority_urls[category] = valid_urls[:urls_per_category]
                            self.logger.info(f"✅ Added {len(priority_urls[category])} URLs for priority category '{category}'", {
                                "category": category,
                                "added_urls": len(priority_urls[category]),
                                "total_available": len(valid_urls),
                                "urls_per_category_limit": urls_per_category
                            })
                        else:
                            self.logger.warning(f"Priority category '{category}' has no valid URLs", {
                                "category": category,
                                "raw_urls": urls
                            })
                    elif category in self.priority_categories:
                        self.logger.warning(f"Priority category '{category}' is empty", {
                            "category": category,
                            "url_count": len(urls)
                        })
                        
                except Exception as category_error:
                    self.logger.error("Error processing category", {"category": category, "error": str(category_error)})
                    continue
            
            # 🚀 FIX 2: Enhanced fallback logic for priority URLs
            if not priority_urls:
                self.logger.warning("No priority URLs found, applying enhanced fallback logic")

                # First try to find any home page or main page URLs
                for category, urls in classified_urls.items():
                    if isinstance(category, str) and isinstance(urls, list):
                        if any(keyword in category.lower() for keyword in ['home', 'main', 'index']):
                            valid_urls = [url for url in urls if isinstance(url, str) and url.strip()]
                            if valid_urls:
                                priority_urls[category] = valid_urls[:urls_per_category]
                                self.logger.info(f"✅ Added fallback category {category}")
                                break

                # If still no priority URLs, use any available category with URLs
                if not priority_urls:
                    self.logger.warning("No home/main URLs found, using any available category")
                    for category, urls in classified_urls.items():
                        if isinstance(category, str) and isinstance(urls, list) and urls:
                            # Skip social media and unreachable categories
                            if not any(skip_word in category.lower() for skip_word in ['unreachable', 'facebook', 'instagram', 'twitter', 'linkedin', 'youtube', 'pinterest']):
                                valid_urls = [url for url in urls if isinstance(url, str) and url.strip()]
                                if valid_urls:
                                    priority_urls[category] = valid_urls[:urls_per_category]
                                    self.logger.info(f"✅ Added any available category {category}")
                                    break
                        
            # 🚀 FIX 3: Final fallback - ensure we always have at least the main page
            final_priority_count = sum(len(urls) for urls in priority_urls.values())
            if final_priority_count == 0:
                self.logger.warning("All fallback attempts failed, using main website URL as final fallback")
                # Extract website URL from classified_urls if available, otherwise use a default
                main_website_url = None

                # Try to find the main website URL from any category
                for category, urls in classified_urls.items():
                    if isinstance(urls, list) and urls:
                        for url in urls:
                            if isinstance(url, str) and url.strip():
                                main_website_url = url.strip()
                                break
                        if main_website_url:
                            break

                # If still no URL found, use the provided website parameter
                if not main_website_url:
                    main_website_url = website if website else "https://example.com"
                    if website:
                        self.logger.info(f"Using provided website parameter: {website}")
                    else:
                        self.logger.error("Could not find any valid URL and no website provided, using placeholder")

                priority_urls = {"home_page": [main_website_url]}
                self.logger.info(f"✅ Final fallback applied: {main_website_url}")

            self.logger.info(
                "Priority URLs filtered",
                {
                    "priority_categories": list(priority_urls.keys()),
                    "total_priority_urls": sum(len(urls) for urls in priority_urls.values()),
                    "fallback_applied": final_priority_count == 0
                }
            )

            return priority_urls
            
        except Exception as e:
            self.logger.error("Error filtering priority URLs", {"error": str(e), "traceback": traceback.format_exc()})
            return default_priority_urls

    async def classify_mcc(self, website: str, priority_urls: Dict[str, List[str]]) -> Tuple[Optional[Dict], Optional[Dict]]:
        """
        Classify MCC using Gemini API with comprehensive error handling
        
        Args:
            website (str): Website URL
            priority_urls (Dict[str, List[str]]): Priority URLs by category
            
        Returns:
            Tuple[Optional[Dict], Optional[Dict]]: (website_info, mcc_info)
        """
        # Default fallback values
        default_website_info = {
            "product_services": "Unable to determine",
            "line_of_business": "General business",
            "customers": "General customers",
            "website_description": "Website information not available"
        }
        
        # Enhanced default values with error context
        default_mcc_info = {
            "mcc": "-1",  # Default MCC for miscellaneous retail
            "business_category": "unable to process site",
            "business_desc": "Unable to determine specific business type",
            "reason": "Default MCC assigned due to analysis failure"
        }

        # Default website info for failures
        default_website_info = {
            "product_services": "insufficient data",
            "line_of_business": "insufficient data",
            "customers": "insufficient data",
            "website_description": "insufficient data"
        }
        
        try:
            # Validate inputs
            if not website or not isinstance(website, str):
                self.logger.error("Invalid website URL provided", {"website": website, "type": type(website)})
                return default_website_info, default_mcc_info
            
            if not priority_urls or not isinstance(priority_urls, dict):
                self.logger.warning("Invalid priority URLs, using website as fallback", {"priority_urls_type": type(priority_urls)})
                priority_urls = {"home_page": [website]}
            
            self.logger.info(
                "Starting MCC classification",
                {"website": website, "priority_categories": list(priority_urls.keys())}
            )
            
            max_retries = 3
            website_info = None
            mcc_info = None
        
            # 🔄 BACKUP FLOW: Check if we have backup data from text extraction
            if hasattr(self, 'backup_website_summary') and self.backup_website_summary:
                self.logger.info("🚀 Using backup flow data from text extraction")
                try:
                    website_info = self.backup_website_summary
                    self.logger.info("✅ Using extracted and summarized content as website info", {
                        "content_keys": list(website_info.keys()) if isinstance(website_info, dict) else "non-dict",
                        "backup_urls_count": len(self.backup_extracted_texts) if hasattr(self, 'backup_extracted_texts') else 0
                    })
                    
                    # Clear backup data after use
                    delattr(self, 'backup_website_summary')
                    if hasattr(self, 'backup_extracted_texts'):
                        delattr(self, 'backup_extracted_texts')
                        
                except Exception as backup_error:
                    self.logger.error("❌ Error using backup data, falling back to normal flow", {
                        "error": str(backup_error)
                    })
                    website_info = None
        
            # Step 1: Extract text content from priority URLs before analysis
            extracted_content = {}
            if website_info is None:  # Only extract if not using backup data
                self.logger.info("🔍 Extracting text content from priority URLs for analysis")

                for category, urls in priority_urls.items():
                    if isinstance(urls, list) and urls:
                        # Extract text from the first URL in each category
                        url = urls[0]
                        self.logger.info(f"📄 Extracting text from {category}: {url}")

                        # Try playwright method first, then fallback to requests
                        text = await self.extract_text_with_method(url, "playwright")
                        if not text or len(text.strip()) <= 50:
                            self.logger.warning(f"⚠️ Playwright extraction insufficient for {category}, trying requests method")
                            text = await self.extract_text_with_method(url, "requests")

                        if text and len(text.strip()) > 50:
                            # Crop text to reasonable length (first 5000 characters)
                            cropped_text = text[:5000] if len(text) > 5000 else text
                            extracted_content[category] = {
                                "url": url,
                                "text": cropped_text,
                                "text_length": len(cropped_text)
                            }
                            self.logger.info(f"✅ Text extracted from {category}: {len(cropped_text)} characters")
                        else:
                            self.logger.warning(f"❌ Failed to extract meaningful text from {category}: {url}")

                self.logger.info(f"📊 Text extraction completed: {len(extracted_content)} categories with content")

            # Step 2: Get website information with comprehensive error handling (skip if backup data used)
            retry_count = 0
            while retry_count < max_retries and website_info is None:
                try:
                    self.logger.info(
                        f"Website information attempt {retry_count + 1}",
                        {"website": website}
                    )

                    # Log the URLs being passed to Gemini for website summary
                    total_urls_for_gemini = sum(len(urls) for urls in priority_urls.values() if isinstance(urls, list))
                    urls_breakdown = {category: len(urls) for category, urls in priority_urls.items() if isinstance(urls, list)}

                    self.logger.info("📊 URLs being passed to Gemini for website summary", {
                        "total_urls_count": total_urls_for_gemini,
                        "urls_breakdown_by_category": urls_breakdown,
                        "priority_urls_structure": {k: v[:2] for k, v in priority_urls.items() if isinstance(v, list)},  # Show first 2 URLs per category
                        "max_urls_for_mcc_limit": self.max_urls_for_mcc,
                        "extracted_content_categories": list(extracted_content.keys()),
                        "total_extracted_text_length": sum(item["text_length"] for item in extracted_content.values())
                    })

                    # Create prompt for website information using extracted content
                    try:
                        if extracted_content:
                            # Use extracted text content for analysis
                            website_info_prompt = self.create_website_info_prompt_with_content(website, extracted_content)
                            self.logger.info("✅ Using extracted text content for website analysis")
                        else:
                            # Fallback to URL-based prompt if no content extracted
                            website_info_prompt = GptPromptPicker.get_website_information_prompt(
                                website, priority_urls
                            )
                            self.logger.warning("⚠️ Using URL-based prompt as fallback (no extracted content)")

                        if not website_info_prompt or not isinstance(website_info_prompt, str):
                            raise ValueError("Invalid or empty prompt generated")
                    except Exception as prompt_error:
                        self.logger.error("Error creating website info prompt", {"error": str(prompt_error)})
                        raise Exception(f"Prompt generation failed: {str(prompt_error)}")

                    # Call Gemini API - this is a synchronous call with timeout
                    self.logger.info("Calling Gemini API for website information...")
                    context_info = {"task_type": "website_info", "website": website, "attempt": retry_count + 1}
                    website_info_response = get_gemini_response_mcc_analysis(website_info_prompt, timeout_seconds=60)
                    
                    # Check for error responses
                    if not website_info_response or website_info_response.startswith("Error:"):
                        raise Exception(f"Gemini API error: {website_info_response}")
                    
                    self.logger.info(f"Website info response length: {len(website_info_response)}")
                    self.logger.info("Received Gemini API response for website information")
                    
                    # Parse response with error handling
                    parsed_response = self.parse_json_response(website_info_response)
                    
                    if not parsed_response or not isinstance(parsed_response, dict):
                        raise Exception("Failed to parse website information response")
                    
                    # Extract and validate website information with defaults
                    website_info = {
                        "product_services": str(parsed_response.get("product_services", default_website_info["product_services"])).strip(),
                        "line_of_business": str(parsed_response.get("line_of_business", default_website_info["line_of_business"])).strip(),
                        "customers": str(parsed_response.get("customers", default_website_info["customers"])).strip(),
                        "website_description": str(parsed_response.get("website_description", default_website_info["website_description"])).strip()
                    }
                    
                    # Ensure no empty strings
                    for key, value in website_info.items():
                        if not value or value.lower() in ['unknown', 'n/a', 'null', 'none']:
                            website_info[key] = default_website_info[key]

                    # Log the complete website summary output
                    self.logger.info("📋 WEBSITE SUMMARY OUTPUT FROM GEMINI", {
                        "website": website,
                        "total_urls_analyzed": total_urls_for_gemini,
                        "summary_content": {
                            "product_services": website_info["product_services"],
                            "line_of_business": website_info["line_of_business"],
                            "customers": website_info["customers"],
                            "website_description": website_info["website_description"]
                        },
                        "summary_lengths": {
                            "product_services_chars": len(website_info["product_services"]),
                            "line_of_business_chars": len(website_info["line_of_business"]),
                            "customers_chars": len(website_info["customers"]),
                            "website_description_chars": len(website_info["website_description"])
                        }
                    })

                    self.logger.info("Website information extracted successfully",
                                   {"info_keys": list(website_info.keys())})
                    
                except Exception as e:
                    retry_count += 1
                    self.logger.error(
                        f"Website information attempt {retry_count} failed",
                        {"error": str(e), "error_type": type(e).__name__}
                    )
                    
                    if retry_count >= max_retries:
                        self.logger.error("All website information attempts failed, using defaults")
                        website_info = default_website_info
                        break
                    
                    time.sleep(5 * retry_count)
        
            # Step 2: Get MCC classification with comprehensive error handling
            retry_count = 0
            while retry_count < max_retries and mcc_info is None:
                try:
                    self.logger.info(
                        f"MCC classification attempt {retry_count + 1}",
                        {"website": website}
                    )
                    
                    # Load and process CSV data files with error handling
                    try:
                        self.logger.info("Loading CSV data files...")
                        
                        # Load special MCCs with error handling
                        try:
                            special_mcc = pd.read_csv('app/input/special_mccs.csv')
                            special_mcc = special_mcc[['mcc','category','description']]
                            self.logger.info(f"Loaded special_mccs.csv with {len(special_mcc)} rows")
                        except Exception as e:
                            self.logger.error("Error loading special_mccs.csv", {"error": str(e)})
                            special_mcc = pd.DataFrame(columns=['mcc','category','description'])

                        # Load combined MCC data with error handling
                        try:
                            combined_mcc = pd.read_csv('app/input/combined_mcc_data(1).csv')
                            combined_mcc = combined_mcc.drop(['not_included_product_services'], axis=1, errors='ignore')
                            self.logger.info(f"Loaded combined_mcc.csv with {len(combined_mcc)} rows")
                        except Exception as e:
                            self.logger.error("Error loading combined_mcc_data.csv", {"error": str(e)})
                            combined_mcc = pd.DataFrame(columns=['mcc','category','description'])
                        
                        # Process MCC data with error handling
                        excluded_mccs = [742, 763, 780, 5399, 7399, 8299, 5818, 5719, 1799, 4789, 5039, 5046, 5099, 5169, 5199, 5599, 5969, 7011, 7299, 7929, 7999, 9399, 5999, 5599, 5499, 8299, 5399, 5719, 7699, 5699, 2741, 1520, 5310]
                        
                        try:
                            if not special_mcc.empty:
                                filtered_special_mcc = special_mcc[~special_mcc['mcc'].astype(str).str.endswith('99')]
                                for mcc in excluded_mccs:
                                    filtered_special_mcc = filtered_special_mcc[filtered_special_mcc['mcc'] != mcc]
                            else:
                                filtered_special_mcc = pd.DataFrame(columns=['mcc','category','description'])
                        except Exception as e:
                            self.logger.error("Error filtering special MCCs", {"error": str(e)})
                            filtered_special_mcc = pd.DataFrame(columns=['mcc','category','description'])
                        
                        try:
                            if not combined_mcc.empty:
                                # Filter out MCCs ending in 99 (similar to special_mcc filtering)
                                filtered_combined_mcc = combined_mcc[~combined_mcc['mcc'].astype(str).str.endswith('99')]
                                # Filter out MCC -1
                                filtered_combined_mcc = filtered_combined_mcc[filtered_combined_mcc['mcc'] != -1]
                                # Filter out excluded MCCs
                                for mcc in excluded_mccs:
                                    filtered_combined_mcc = filtered_combined_mcc[filtered_combined_mcc['mcc'] != mcc]
                            else:
                                filtered_combined_mcc = pd.DataFrame(columns=['mcc','category','description'])
                        except Exception as e:
                            self.logger.error("Error filtering combined MCCs", {"error": str(e)})
                            filtered_combined_mcc = pd.DataFrame(columns=['mcc','category','description'])
                        
                        self.logger.info("CSV data processing completed")
                        
                    except Exception as csv_error:
                        self.logger.error("Major error processing CSV files", {"error": str(csv_error)})
                        # Use empty dataframes as fallback
                        filtered_special_mcc = pd.DataFrame(columns=['mcc','category','description'])
                        filtered_combined_mcc = pd.DataFrame(columns=['mcc','category','description'])
                        excluded_mccs = []

                    # Create prompt for MCC classification
                    try:
                        mcc_prompt = GptPromptPicker.get_website_mcc_classifier_prompt_full(
                            mcc_info=None,
                            website_info=website_info if website_info else default_website_info, 
                            excluded_mccs=excluded_mccs, 
                            filtered_special_mcc=filtered_special_mcc.to_dict(orient='records'), 
                            filtered_visa_data=filtered_combined_mcc.to_dict(orient='records'),
                            website_url=website
                        )
                        
                        if not mcc_prompt or not isinstance(mcc_prompt, str):
                            raise ValueError("Invalid or empty MCC prompt generated")
                            
                    except Exception as prompt_error:
                        self.logger.error("Error creating MCC classification prompt", {"error": str(prompt_error)})
                        raise Exception(f"MCC prompt generation failed: {str(prompt_error)}")
                    
                    # Call Gemini API - this is a synchronous call with timeout
                    self.logger.info("Calling Gemini API for MCC classification...")
                    context_info = {"task_type": "mcc_classification", "website": website, "attempt": retry_count + 1}
                    mcc_response = get_gemini_response_mcc_analysis(mcc_prompt, timeout_seconds=60)
                    
                    # Check for error responses
                    if not mcc_response or mcc_response.startswith("Error:"):
                        raise Exception(f"Gemini API error: {mcc_response}")
                    
                    self.logger.info("Received Gemini API response for MCC classification")
                    
                    # Parse response with error handling
                    parsed_mcc_response = self.parse_json_response(mcc_response)
                    
                    if not parsed_mcc_response or not isinstance(parsed_mcc_response, dict):
                        raise Exception("Failed to parse MCC classification response")
                    
                    # Extract and validate MCC information with defaults
                    mcc_info = {
                        "mcc": parsed_mcc_response.get("mcc", default_mcc_info["mcc"]),
                        "business_category": str(parsed_mcc_response.get("business_category", default_mcc_info["business_category"])).strip(),
                        "business_desc": str(parsed_mcc_response.get("business_desc", default_mcc_info["business_desc"])).strip(),
                        "reason": str(parsed_mcc_response.get("reason", parsed_mcc_response.get("reasoning", default_mcc_info["reason"]))).strip()
                    }
                    
                    # Validate MCC code
                    try:
                        mcc_code = int(mcc_info["mcc"])
                        if mcc_code <= 0 or mcc_code > 9999:
                            self.logger.warning("Invalid MCC code, using default", {"mcc": mcc_code})
                            mcc_info["mcc"] = default_mcc_info["mcc"]
                    except (ValueError, TypeError):
                        self.logger.warning("Non-numeric MCC code, using default", {"mcc": mcc_info["mcc"]})
                        mcc_info["mcc"] = default_mcc_info["mcc"]
                    
                    # Ensure no empty strings
                    for key, value in mcc_info.items():
                        if key != "mcc" and (not value or value.lower() in ['unknown', 'n/a', 'null', 'none']):
                            mcc_info[key] = default_mcc_info[key]
                    
                    self.logger.info(
                        "MCC classification retrieved successfully",
                        {
                            "mcc": mcc_info.get("mcc"),
                            "business_desc": mcc_info.get("business_desc"),
                            "business_category": mcc_info.get("business_category"),
                            "reason": mcc_info.get("reason")
                        }
                    )
                    
                except Exception as e:
                    retry_count += 1
                    self.logger.error(
                        f"MCC classification attempt {retry_count} failed",
                        {"error": str(e), "error_type": type(e).__name__}
                    )
                    
                    if retry_count >= max_retries:
                        self.logger.error("All MCC classification attempts failed, using defaults")
                        mcc_info = default_mcc_info
                        break
                    
                    time.sleep(5 * retry_count)
            
            return website_info, mcc_info
            
        except Exception as e:
            self.logger.error("Unexpected error in MCC classification", {"error": str(e), "traceback": traceback.format_exc()})
            return default_website_info, default_mcc_info
    
    def parse_json_response(self, response_text: str) -> Optional[Dict]:
        """
        Parse JSON response from API with comprehensive error handling
        
        Args:
            response_text (str): API response text
            
        Returns:
            Optional[Dict]: Parsed JSON or None
        """
        try:
            # Validate input
            if not response_text or not isinstance(response_text, str):
                self.logger.error("Invalid response text provided", {"response_type": type(response_text)})
                return None
            
            response_text = response_text.strip()
            
            # Handle empty response
            if not response_text:
                self.logger.warning("Empty response text after stripping")
                return None
            
            # Remove markdown code blocks if present
            original_text = response_text
            try:
                if response_text.startswith("```json"):
                    response_text = response_text[7:]
                elif response_text.startswith("```"):
                    # Split only once to avoid issues with nested code blocks
                    split_response = response_text.split("\n", 1)
                    if len(split_response) > 1:
                        response_text = split_response[1]
                    else:
                        response_text = response_text[3:]  # Remove just the ```
                
                if response_text.endswith("```"):
                    response_text = response_text[:-3]
                
                response_text = response_text.strip()
                
            except Exception as clean_error:
                self.logger.warning("Error cleaning markdown, using original text", {"error": str(clean_error)})
                response_text = original_text.strip()
            
            # Final validation
            if not response_text:
                self.logger.warning("Empty response after markdown cleanup")
                return None
            
            # Parse JSON with detailed error handling
            try:
                parsed_json = json.loads(response_text)
                
                # Validate that we got a dictionary
                if not isinstance(parsed_json, dict):
                    self.logger.warning("Parsed JSON is not a dictionary", {"type": type(parsed_json)})
                    return None
                
                self.logger.info("Successfully parsed JSON response", {"keys": list(parsed_json.keys())})
                return parsed_json
                
            except json.JSONDecodeError as json_error:
                self.logger.error("JSON decode error", {
                    "error": str(json_error),
                    "line": getattr(json_error, 'lineno', 'unknown'),
                    "column": getattr(json_error, 'colno', 'unknown'),
                    "response_sample": response_text[:200] + "..." if len(response_text) > 200 else response_text
                })
                return None
            
        except Exception as e:
            self.logger.error("Unexpected error processing response", {
                "error": str(e),
                "error_type": type(e).__name__,
                "response_length": len(response_text) if isinstance(response_text, str) else 0
            })
            return None
    
    async def save_mcc_url_classifications(
        self,
        analysis_id: int,
        soft_classified_urls: Dict,
        hard_classified_urls: Dict
    ) -> bool:
        """
        Save detailed MCC URL classification results to database

        Args:
            analysis_id (int): MCC analysis ID
            soft_classified_urls (Dict): Soft classification results
            hard_classified_urls (Dict): Hard classification results

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            self.logger.info(
                "Saving MCC URL classification results",
                {"analysis_id": analysis_id, "soft_categories": len(soft_classified_urls), "hard_categories": len(hard_classified_urls)}
            )

            # Get all URLs for depth information
            urls_query = select(WebsiteUrls).where(
                WebsiteUrls.scrape_request_ref_id == self.scrape_request_ref_id
            )
            result = await self.db_session.execute(urls_query)
            url_records = result.scalars().all()
            url_depth_map = {url_record.url: url_record.depth for url_record in url_records}

            # Process all URLs from both soft and hard classification
            all_urls = set()

            # Collect all URLs from soft classification
            for category, urls in soft_classified_urls.items():
                if isinstance(urls, list):
                    all_urls.update(urls)

            # Collect all URLs from hard classification
            for category, urls in hard_classified_urls.items():
                if isinstance(urls, list):
                    all_urls.update(urls)

            # Save classification for each URL
            saved_count = 0
            for url in all_urls:
                # Find soft classification categories for this URL
                soft_categories = []
                for category, urls_list in soft_classified_urls.items():
                    if isinstance(urls_list, list) and url in urls_list:
                        soft_categories.append(category)

                # Find hard classification categories for this URL
                hard_categories = []
                for category, urls_list in hard_classified_urls.items():
                    if isinstance(urls_list, list) and url in urls_list:
                        hard_categories.append(category)

                # Determine final classification (prefer hard over soft)
                final_categories = hard_categories if hard_categories else soft_categories

                # Check if this is a priority URL
                is_priority = any(cat in self.priority_categories for cat in final_categories)

                # Create URL classification record
                url_classification = MccUrlClassification(
                    mcc_analysis_id=analysis_id,
                    scrape_request_ref_id=self.scrape_request_ref_id,
                    url=str(url),
                    url_depth=url_depth_map.get(url, 1),
                    soft_classification=",".join(soft_categories),
                    hard_classification=",".join(hard_categories),
                    final_classification=",".join(final_categories),
                    is_priority_url=is_priority,
                    is_social_media=any("page" in cat for cat in final_categories),
                    org_id=self.org_id
                )

                self.db_session.add(url_classification)
                saved_count += 1

            # Commit the URL classifications
            await self.db_session.commit()

            self.logger.info(
                "MCC URL classification results saved successfully",
                {"analysis_id": analysis_id, "urls_saved": saved_count}
            )

            return True

        except Exception as e:
            self.logger.error("Error saving MCC URL classification results", error=e)
            await self.db_session.rollback()
            return False

    async def save_mcc_analysis_results(
        self,
        website: str,
        classified_urls: Dict,
        website_info: Dict,
        mcc_info: Dict,
        flow_indicators: Optional[Dict] = None
    ) -> int:
        """
        Save MCC analysis results to database
        
        Args:
            website (str): Website URL
            classified_urls (Dict): Classified URLs
            website_info (Dict): Website information
            mcc_info (Dict): MCC classification information
            
        Returns:
            int: Analysis ID
        """
        self.logger.info(
            "Saving MCC analysis results to database",
            {"website": website}
        )
        
        try:
            # Validate inputs with defaults
            if not website or not isinstance(website, str):
                website = "Unknown website"
            
            if not isinstance(classified_urls, dict):
                classified_urls = {}
            
            if not isinstance(website_info, dict):
                website_info = {}
            
            if not isinstance(mcc_info, dict):
                mcc_info = {}
            
            # Create clean output with error handling
            try:
                clean_output = {
                    "website": website,
                    "scrape_request_ref_id": self.scrape_request_ref_id,
                    "website_info": website_info,
                    "mcc_info": mcc_info,
                    "classified_urls": classified_urls,
                    "analysis_timestamp": datetime.now().isoformat(),
                    "org_id": self.org_id,
                }
                
                # Add flow indicators if provided
                if flow_indicators and isinstance(flow_indicators, dict):
                    clean_output.update(flow_indicators)
            except Exception as output_error:
                self.logger.error("Error creating clean output", {"error": str(output_error)})
                clean_output = {
                    "website": website,
                    "scrape_request_ref_id": self.scrape_request_ref_id,
                    "analysis_timestamp": datetime.now().isoformat(),
                    "org_id": self.org_id,
                    "error": "Failed to create complete output"
                }
            
            # Get primary MCC code - handle both old format (mcc_codes array) and new format (mcc integer)
            primary_mcc = None
            try:
                if mcc_info and isinstance(mcc_info, dict):
                    # Check if the new format (mcc as integer) is used
                    if "mcc" in mcc_info:
                        primary_mcc = mcc_info.get("mcc")
                        # Convert to string if it's an integer
                        if isinstance(primary_mcc, int):
                            primary_mcc = str(primary_mcc)
                        elif primary_mcc is not None and not isinstance(primary_mcc, str):
                            primary_mcc = str(primary_mcc)
                    # Fallback to old format (mcc_codes array)
                    else:
                        mcc_codes = mcc_info.get("mcc_codes", [])
                        if isinstance(mcc_codes, list) and len(mcc_codes) > 0:
                            primary_mcc = str(mcc_codes[0]) if mcc_codes[0] is not None else None
                else:
                    primary_mcc = None
            except Exception as mcc_error:
                self.logger.error("Error extracting primary MCC", {"error": str(mcc_error)})
                primary_mcc = None
            
            # Find existing MCC analysis record with error handling
            try:
                from sqlmodel import select
                from app.models.db_models import MccAnalysis
                
                db_result = await self.db_session.execute(
                    select(MccAnalysis).where(
                        MccAnalysis.scrape_request_ref_id == self.scrape_request_ref_id
                    )
                )
                result = db_result.scalar_one_or_none()
                
            except Exception as db_query_error:
                self.logger.error("Error querying existing MCC analysis", {"error": str(db_query_error)})
                raise Exception(f"Database query failed: {str(db_query_error)}")
            
            if result:
                # Update existing record - result is already the model instance
                mcc_analysis = result
                mcc_analysis.mcc_code = primary_mcc
                
                # Handle insufficient data case
                if primary_mcc == "-1":
                    mcc_analysis.business_description = "insufficient data"
                    mcc_analysis.business_category = "insufficient data"
                    mcc_analysis.reasoning = "insufficient data"
                else:
                    mcc_analysis.business_description = website_info.get("business_description", "") if website_info else ""
                    mcc_analysis.business_category = mcc_info.get("business_category", "") if mcc_info else ""
                    mcc_analysis.reasoning = mcc_info.get("reason", mcc_info.get("reasoning", "")) if mcc_info else ""
                
                mcc_analysis.details = json.dumps(clean_output)
                mcc_analysis.completed_at = get_current_time()
                mcc_analysis.processing_status = "COMPLETED"
                analysis_id = mcc_analysis.id
            else:
                
                # Handle insufficient data case for new records
                if primary_mcc == "-1":
                    business_description = "insufficient data"
                    business_category = "insufficient data"
                    reasoning = "insufficient data"
                else:
                    business_description = website_info.get("business_description", "") if website_info else ""
                    business_category = mcc_info.get("business_category", "") if mcc_info else ""
                    reasoning = mcc_info.get("reason", mcc_info.get("reasoning", "")) if mcc_info else ""
                
                # Create new MCC analysis record (no website_id needed)
                mcc_analysis = MccAnalysis(
                    website=website,
                    scrape_request_ref_id=self.scrape_request_ref_id,
                    mcc_code=primary_mcc,  # Store just the primary MCC code
                    business_description=business_description,
                    business_category=business_category,
                    reasoning=reasoning,
                    details=json.dumps(clean_output),
                    created_at=get_current_time(),
                    started_at=get_current_time(),
                    completed_at=get_current_time(),
                    processing_status="COMPLETED",
                    org_id=self.org_id
                )
                self.db_session.add(mcc_analysis)
                await self.db_session.flush()  # Get ID without committing
                analysis_id = mcc_analysis.id
            
            # Commit all changes with error handling
            try:
                await self.db_session.commit()
                self.logger.info(
                    "MCC analysis results saved successfully",
                    {"analysis_id": analysis_id}
                )
                
                # Send webhook notification for successful MCC analysis
                try:
                    # Prepare MCC result data for webhook
                    mcc_result = {
                        "mcc": primary_mcc,
                        "businessCategory": mcc_info.get("business_category", "") if mcc_info else "",
                        "businessDescription": website_info.get("business_description", "") if website_info else "",
                        "status": "COMPLETED"
                    }
                    
                    # Send webhook
                    webhook_success = send_mcc_results_webhook(
                        scrape_request_ref_id=self.scrape_request_ref_id,
                        website=website,
                        mcc_result=mcc_result,
                        analysis_id=analysis_id,
                        logger=self.logger
                    )
                    
                    if webhook_success:
                        self.logger.info("✅ MCC results webhook sent successfully", {"analysis_id": analysis_id})
                    else:
                        self.logger.warning("⚠️ MCC results webhook failed", {"analysis_id": analysis_id})
                        
                except Exception as webhook_error:
                    self.logger.error("❌ Error sending MCC results webhook", {
                        "error": str(webhook_error),
                        "analysis_id": analysis_id
                    })
                    # Don't fail the entire process if webhook fails
                
                return analysis_id
                
            except Exception as commit_error:
                self.logger.error("Error committing MCC analysis results", {"error": str(commit_error)})
                try:
                    await self.db_session.rollback()
                    self.logger.info("Successfully rolled back database transaction")
                except Exception as rollback_error:
                    self.logger.error("Error during rollback", {"error": str(rollback_error)})
                raise Exception(f"Database commit failed: {str(commit_error)}")
            
        except Exception as e:
            self.logger.error("Error saving MCC analysis results", {"error": str(e), "error_type": type(e).__name__})
            
            # Attempt rollback if session is still active
            try:
                if self.db_session and hasattr(self.db_session, 'rollback'):
                    await self.db_session.rollback()
                    self.logger.info("Rolled back transaction after error")
            except Exception as rollback_error:
                self.logger.error("Error during emergency rollback", {"error": str(rollback_error)})
            
            raise
    
    async def handle_analysis_error(self, error: Exception, step: str) -> None:
        """
        Handle analysis errors and update database status

        Args:
            error (Exception): The error that occurred
            step (str): The step where error occurred
        """
        self.logger.error(f"MCC analysis failed at step: {step}", error=error)

        try:
            # Update MccAnalysis with error status
            from sqlmodel import select
            from app.models.db_models import MccAnalysis

            result = await self.db_session.execute(
                select(MccAnalysis).where(
                    MccAnalysis.scrape_request_ref_id == self.scrape_request_ref_id
                )
            )
            mcc_analysis = result.scalar_one_or_none()

            if mcc_analysis:
                mcc_analysis.processing_status = "FAILED"
                mcc_analysis.error_message = f"Error at {step}: {str(error)}"
                mcc_analysis.completed_at = get_current_time()
                await self.db_session.commit()

        except Exception as db_error:
            self.logger.error("Error updating MCC analysis status", error=db_error)
            await self.db_session.rollback()
    
    async def process_mcc_analysis(self, request_data: Optional[MccAnalysisRequest] = None) -> Dict:
        """
        Main method to process complete MCC analysis flow
        
        Args:
            request_data (Optional[MccAnalysisRequest]): MCC analysis request data.
                If provided, will use this data directly instead of fetching from DB.
            
        Returns:
            Dict: Analysis results with status and details
        """
        if request_data:
            self.logger.info(
                "Starting MCC analysis process with provided request data",
                {
                    "website": request_data.website,
                    "scrape_request_ref_id": request_data.scrapeRequestRefID,
                    "org_id": request_data.org_id
                }
            )
        else:
            self.logger.info(
                "Starting MCC analysis process",
                {
                    "scrape_request_ref_id": self.scrape_request_ref_id,
                    "org_id": self.org_id
                }
            )
        
        start_time = time.time()
        website = "unknown"  # Default value in case it's not set later
        
        try:
            # Step 1: Get URLs data - either from request or database
            self.logger.info("Step 1: Getting URLs data")
            
            if request_data:
                # Use request data directly
                urls_data = {
                    "website": request_data.website,
                    "parsed_urls": [
                        {
                            "url_depth": item.url_depth,
                            "urls": [str(url) for url in item.urls]
                        }
                        for item in request_data.parsed_urls
                    ]
                }
                website = request_data.website
            else:
                # Check if URLs exist in database
                # Protected tuple unpacking for URL check
                try:
                    url_check_result = await self.check_urls_exist_in_db()
                    
                    if url_check_result is None:
                        self.logger.error("check_urls_exist_in_db returned None instead of tuple")
                        raise ValueError("check_urls_exist_in_db returned None")
                    
                    if not isinstance(url_check_result, tuple):
                        self.logger.error(f"check_urls_exist_in_db returned {type(url_check_result)} instead of tuple")
                        raise ValueError(f"Expected tuple, got {type(url_check_result)}")
                    
                    if len(url_check_result) != 2:
                        self.logger.error(f"check_urls_exist_in_db returned tuple of length {len(url_check_result)}")
                        raise ValueError(f"Expected tuple of length 2, got {len(url_check_result)}")
                    
                    urls_exist, urls_data = url_check_result
                    
                    self.logger.info("Successfully unpacked URL check result", {
                        "urls_exist": urls_exist,
                        "urls_data_type": type(urls_data)
                    })
                    
                except Exception as url_unpack_error:
                    self.logger.error("Critical error in URL check unpacking", {
                        "error": str(url_unpack_error),
                        "error_type": type(url_unpack_error).__name__,
                        "traceback": traceback.format_exc()
                    })
                    raise Exception(f"URL check unpacking failed: {str(url_unpack_error)}")
                    
                if not urls_exist or not urls_data:
                    self.logger.warning("No URLs found in database for analysis")
                    # Return insufficient data result instead of raising exception
                    return await self.handle_insufficient_urls("unknown_website")
                
                website = urls_data["website"]
            
            # NEW: Check if parsed URLs are <= 1
            total_urls = 0
            for parsed_url_obj in urls_data.get("parsed_urls", []):
                total_urls += len(parsed_url_obj.get("urls", []))
            
            self.logger.info(f"Total parsed URLs found: {total_urls}")
            
            if total_urls <= 1:
                self.logger.warning(
                    "Insufficient URLs for MCC analysis (≤1). Setting MCC to -1 and default values.",
                    {"total_urls": total_urls, "website": website}
                )
                
                # Create insufficient data results
                insufficient_data_result = await self.handle_insufficient_urls(website)
                return insufficient_data_result
            
            # Step 2: Prepare data for analysis
            self.logger.info("Step 2: Preparing data for analysis")
            step2_start = time.time()
            
            # Protected tuple unpacking with detailed error handling
            try:
                result = await self.prepare_data_for_analysis(website, urls_data)
                
                # Detailed validation of the result
                if result is None:
                    self.logger.error("prepare_data_for_analysis returned None instead of tuple")
                    raise ValueError("prepare_data_for_analysis returned None")
                
                if not isinstance(result, tuple):
                    self.logger.error(f"prepare_data_for_analysis returned {type(result)} instead of tuple: {result}")
                    raise ValueError(f"Expected tuple, got {type(result)}")
                
                if len(result) != 3:
                    self.logger.error(f"prepare_data_for_analysis returned tuple of length {len(result)} instead of 3: {result}")
                    raise ValueError(f"Expected tuple of length 3, got {len(result)}")

                classified_urls, priority_urls, soft_classified_urls = result
                
                self.logger.info("Successfully unpacked data preparation result", {
                    "classified_urls_type": type(classified_urls),
                    "priority_urls_type": type(priority_urls),
                    "classified_urls_is_none": classified_urls is None,
                    "priority_urls_is_none": priority_urls is None
                })
                
            except Exception as unpack_error:
                self.logger.error("Critical error in data preparation unpacking", {
                    "error": str(unpack_error),
                    "error_type": type(unpack_error).__name__,
                    "traceback": traceback.format_exc()
                })
                raise Exception(f"Data preparation unpacking failed: {str(unpack_error)}")
            step2_time = time.time() - step2_start
            self.logger.info(f"Step 2 completed in {step2_time:.2f} seconds")
            
            # Check if all URLs are unreachable (prepare_data_for_analysis returns None, None, None)
            if classified_urls is None and priority_urls is None and soft_classified_urls is None:
                self.logger.error("All URLs are truly unreachable - triggering site unreachable backup flow", {
                    "website": website,
                    "backup_flow": "site_unreachable",
                    "time": datetime.now().isoformat()
                })
                
                # DEBUG: Additional logging to help track the flow execution
                self.logger.info(
                    "DEBUG - URL status checkpoint for troubleshooting",
                    {
                        "step": "before_site_unreachable_handler", 
                        "condition_triggered": "classified_urls=None,priority_urls=None",
                        "normal_flow_bypassed": True,
                        "next_step": "handle_site_unreachable",
                        "websie": website,
                        "scrape_ref": self.scrape_request_ref_id
                    }
                )
                
                return await self.handle_site_unreachable(website)
            
            # Validate priority URLs with detailed logging
            if not priority_urls:
                self.logger.warning("No priority URLs found after classification - triggering insufficient data backup flow", {
                    "website": website,
                    "classified_urls_available": classified_urls is not None,
                    "backup_flow": "insufficient_data"
                })
                return await self.handle_insufficient_urls(website)
            
            # Additional validation to prevent race conditions
            priority_url_count = sum(len(urls) for urls in priority_urls.values() if isinstance(urls, list))
            if priority_url_count == 0:
                self.logger.warning("Priority URLs exist but are empty - triggering insufficient data backup flow", {
                    "website": website,
                    "priority_urls_structure": priority_urls,
                    "backup_flow": "insufficient_data"
                })
                return await self.handle_insufficient_urls(website)
            
            # Step 3: Classify MCC with enhanced error handling
            self.logger.info("Step 3: Starting MCC classification", {
                "website": website,
                "priority_url_categories": list(priority_urls.keys()),
                "total_priority_urls": priority_url_count
            })
            
            step3_start = time.time()
            
            try:
                website_info, mcc_info = await self.classify_mcc(website, priority_urls)
                step3_time = time.time() - step3_start
                
                self.logger.info(f"Step 3: MCC classification completed in {step3_time:.2f} seconds", {
                    "website_info_available": website_info is not None,
                    "mcc_info_available": mcc_info is not None,
                    "processing_time": f"{step3_time:.2f}s"
                })
                
                if not mcc_info:
                    self.logger.warning("MCC classification returned no MCC info, but continuing with available data", {
                        "website_info": website_info is not None,
                        "will_use_defaults": True
                    })
                    
            except Exception as mcc_classify_error:
                step3_time = time.time() - step3_start
                self.logger.error("Step 3: MCC classification failed with exception", {
                    "error": str(mcc_classify_error),
                    "error_type": type(mcc_classify_error).__name__,
                    "processing_time": f"{step3_time:.2f}s",
                    "will_use_defaults": True
                })
                # Set enhanced default values to prevent race condition
                website_info = default_website_info.copy()
                website_info["website_description"] = f"MCC classification error: {str(mcc_classify_error)[:100]}"

                mcc_info = default_mcc_info.copy()
                mcc_info.update({
                    "business_category": "classification_error",
                    "business_desc": f"Default MCC due to classification error: {type(mcc_classify_error).__name__}",
                    "reason": f"MCC classification failed: {str(mcc_classify_error)[:200]}"
                })
            
            # NEW: Secondary backup flow trigger - check if website content indicates site is not active
            if website_info and isinstance(website_info, dict):
                inactive_indicators = ["site is not active", "site is not reachable", "website is not accessible", "not reachable", "not accessible"]
                content_values = [
                    website_info.get("product_services", ""),
                    website_info.get("line_of_business", ""), 
                    website_info.get("customers", ""),
                    website_info.get("website_description", "")
                ]
                
                # Check if ALL content fields indicate site is not active
                inactive_count = 0
                valid_content_count = 0
                for value in content_values:
                    if value and isinstance(value, str) and len(value.strip()) > 0:
                        valid_content_count += 1
                        if any(indicator in value.lower() for indicator in inactive_indicators):
                            inactive_count += 1
                
                # Trigger backup flow if majority of content indicates site is not active
                if valid_content_count >= 3 and inactive_count >= 3:
                    self.logger.error("🔄 SECONDARY BACKUP TRIGGER: Website content analysis indicates site is not active", {
                        "website": website,
                        "content_analysis": dict(zip(["product_services", "line_of_business", "customers", "website_description"], content_values)),
                        "trigger_reason": "website_content_indicates_inactive",
                        "inactive_fields": inactive_count,
                        "total_valid_fields": valid_content_count,
                        "backup_flow": "handle_original_site_unreachable",
                        "primary_url_classification_missed": True,
                        "scrape_ref": self.scrape_request_ref_id,
                        "time": datetime.now().isoformat()
                    })
                    
                    # Override the normal flow and trigger backup method
                    return await self.handle_original_site_unreachable(website)
                else:
                    self.logger.info("✅ Website content appears valid - continuing with normal flow", {
                        "inactive_fields": inactive_count,
                        "valid_fields": valid_content_count,
                        "threshold_check": "passed"
                    })
            
            # Step 4: Save results to database
            self.logger.info("Step 4: Saving results to database")
            step4_start = time.time()
            
            # Flow indicators for successful analysis (use backup indicators if available)
            if hasattr(self, 'backup_flow_indicators'):
                flow_indicators = self.backup_flow_indicators
                # Clean up backup indicators after use
                delattr(self, 'backup_flow_indicators')
            else:
                flow_indicators = {
                    "analysis_flow_type": "standard_analysis",
                    "content_availability_status": "content_available",
                    "fallback_method_used": False,
                    "text_extraction_used": False,
                    "insufficient_data": False
                }
            
            analysis_id = await self.save_mcc_analysis_results(
                website, classified_urls, website_info, mcc_info or {}, flow_indicators
            )

            # Save detailed URL classification results
            if analysis_id and soft_classified_urls and classified_urls:
                self.logger.info("Saving detailed MCC URL classification results")
                url_classification_saved = await self.save_mcc_url_classifications(
                    analysis_id, soft_classified_urls, classified_urls
                )
                if url_classification_saved:
                    self.logger.info("MCC URL classification results saved successfully")
                else:
                    self.logger.warning("Failed to save MCC URL classification results")

            step4_time = time.time() - step4_start
            self.logger.info(f"Step 4 completed in {step4_time:.2f} seconds")
            
            processing_time = time.time() - start_time
            
            result = {
                "status": "COMPLETED",
                "analysis_id": analysis_id,
                "website": website,
                "scrape_request_ref_id": self.scrape_request_ref_id,
                "website_info": website_info,
                "mcc_info": mcc_info,
                "classified_urls": classified_urls,
                "priority_urls": priority_urls,
                "reasoning": mcc_info.get("reason", mcc_info.get("reasoning", "")) if mcc_info else "",
                "processing_time": processing_time,
                "org_id": self.org_id,
                # Flow indicators
                "analysis_flow_type": "standard_analysis",
                "content_availability_status": "content_available",
                "fallback_method_used": False,
                "text_extraction_used": False,
                "insufficient_data": False
            }
            
            # Extract MCC for logging - handle both formats
            mcc_for_logging = []
            if mcc_info:
                if "mcc" in mcc_info and mcc_info["mcc"] is not None:
                    mcc_for_logging = [mcc_info["mcc"]]
                elif "mcc_codes" in mcc_info:
                    mcc_for_logging = mcc_info.get("mcc_codes", [])
            
            self.logger.info(
                "MCC analysis completed successfully",
                {
                    "analysis_id": analysis_id,
                    "processing_time": f"{processing_time:.2f}s",
                    "mcc": mcc_for_logging
                }
            )
            
            return result
            
        except Exception as e:
            await self.handle_analysis_error(e, "mcc_analysis")
            processing_time = time.time() - start_time
            
            error_result = {
                "status": "FAILED",
                "error": str(e),
                "website": website,
                "scrape_request_ref_id": self.scrape_request_ref_id,
                "processing_time": processing_time,
                "org_id": self.org_id,
                # Flow indicators for failed analysis
                "analysis_flow_type": "analysis_failed",
                "content_availability_status": "analysis_error",
                "fallback_method_used": False,
                "text_extraction_used": False,
                "insufficient_data": False
            }
            
            self.logger.error(
                "MCC analysis failed",
                {
                    "error": str(e),
                    "processing_time": f"{processing_time:.2f}s",
                    "traceback": traceback.format_exc()
                }
            )
            
            return error_result

    async def execute_backup_flow_with_text_extraction(
        self,
        website: str,
        unreachable_urls: List[str],
        soft_classified_urls: Dict[str, List[str]] = None,
    ) -> Optional[Tuple[Dict, Dict, Dict]]:
        """
        Execute backup flow by extracting text from top 3 URLs from soft classification and processing them

        Args:
            website (str): Website URL
            unreachable_urls (List[str]): List of unreachable URLs (not indices)
            soft_classified_urls (Dict[str, List[str]]): Soft classification results with categorized URLs

        Returns:
            Optional[Tuple[Dict, Dict, Dict]]: (classified_urls, priority_urls, soft_classified_urls) or None if failed
        """
        try:
            self.logger.info("🚀 Starting backup flow with text extraction from top 3 URLs from soft classification")

            # Get top 3 URLs from soft classification priority categories
            top_3_urls = []
            priority_categories = ["home_page", "about_us", "catalogue", "terms_and_condition", "privacy_policy", "contact_us"]

            if soft_classified_urls:
                self.logger.info("📋 Using soft classification results to select top 3 URLs", {
                    "available_categories": list(soft_classified_urls.keys()),
                    "priority_categories": priority_categories
                })

                # Extract URLs from priority categories in order
                for category in priority_categories:
                    if category in soft_classified_urls and isinstance(soft_classified_urls[category], list):
                        category_urls = soft_classified_urls[category]
                        if category_urls:
                            # Take first URL from this category
                            top_3_urls.append(category_urls[0])
                            self.logger.info(f"✅ Selected URL from {category}: {category_urls[0]}")

                            # Stop when we have 3 URLs
                            if len(top_3_urls) >= 3:
                                break

            # Fallback: if we don't have 3 URLs from soft classification, add main website URL
            if len(top_3_urls) == 0:
                top_3_urls.append(website)
                self.logger.warning("⚠️ No URLs from soft classification, using main website URL as fallback")

            # Ensure we have at least one URL
            if not top_3_urls:
                self.logger.error("❌ No valid URLs found for backup flow text extraction")
                return None, None, None

            self.logger.info(f"📝 Processing {len(top_3_urls)} URLs for text extraction: {top_3_urls}")

            # Extract text from each URL
            extracted_texts = []
            processed_urls = []

            for idx, url in enumerate(top_3_urls):
                try:
                    processed_urls.append(url)

                    self.logger.info(f"🔍 Extracting text from URL {idx + 1}/{len(top_3_urls)}: {url}")

                    # Try playwright method first (supports proxy), then fallback to requests
                    text = await self.extract_text_with_method(url, "playwright")
                    if not text or len(text.strip()) <= 50:
                        self.logger.warning(f"⚠️ Playwright extraction insufficient, trying requests method for URL {idx}")
                        text = await self.extract_text_with_method(url, "requests")

                    if text and len(text.strip()) > 50:  # Minimum text threshold
                        # Crop text to limit
                        cropped_text = self.crop_text_to_limit(text, word_limit=15000)
                        extracted_texts.append({
                            "url": url,
                            "index": idx,
                            "text": cropped_text,
                            "text_length": len(cropped_text)
                        })
                        self.logger.info(f"✅ Successfully extracted {len(cropped_text)} characters from URL {idx + 1}")
                    else:
                        self.logger.warning(f"⚠️ Insufficient text extracted from URL {idx + 1}: {len(text) if text else 0} characters")

                except Exception as url_error:
                    self.logger.error(f"❌ Error extracting text from URL {idx + 1}: {str(url_error)}")
                    continue
            
            # Check if we have at least one successful extraction
            if not extracted_texts:
                self.logger.error("❌ No text could be extracted from any of the top 3 URLs")
                return None, None, None
            
            self.logger.info(f"📄 Successfully extracted text from {len(extracted_texts)} URLs")
            
            # Combine all extracted texts for summarization
            combined_text = "\n\n".join([f"URL {item['index']}: {item['url']}\n{item['text']}" for item in extracted_texts])
            
            # Summarize the combined content
            self.logger.info("🧠 Summarizing extracted content using Gemini")
            website_summary = self.summarize_website_content(combined_text, website)
            
            if not website_summary:
                self.logger.error("❌ Failed to summarize extracted content")
                return None, None, None
            
            self.logger.info("✅ Content summarization completed successfully")
            
            # Get website info using the get_website_info prompt
            self.logger.info("📊 Processing website info using get_website_info prompt")
            
            # Create priority URLs dict from the processed URLs
            priority_urls = {
                "home_page": processed_urls[:1],  # Use first URL as home page
                "about_us": processed_urls[1:2] if len(processed_urls) > 1 else [],
                "catalogue": processed_urls[2:3] if len(processed_urls) > 2 else []
            }
            
            # Get website information using the summarized content
            # Store the summary for later use in MCC classification
            self.backup_website_summary = website_summary
            self.backup_extracted_texts = extracted_texts
            
            self.logger.info("💾 Stored backup content for MCC processing - continuing with normal flow")
            
            # Create classified URLs dict
            classified_urls = {
                "home_page": processed_urls[:1],
                "about_us": processed_urls[1:2] if len(processed_urls) > 1 else [],
                "catalogue": processed_urls[2:3] if len(processed_urls) > 2 else [],
                "terms_and_condition": [],
                "returns_cancellation_exchange": [],
                "privacy_policy": [],
                "shipping_delivery": [],
                "contact_us": [],
                "products": [],
                "services": [],
                "contact_page": [],
                "pinterest_page": [],
                "instagram_page": [],
                "facebook_page": [],
                "youtube_page": [],
                "linkedin_page": [],
                "twitter_page": [],
                "urls_not_reachable": []  # Clear this since we successfully processed URLs
            }
            
            # Store flow indicators for backup method
            self.backup_flow_indicators = {
                "analysis_flow_type": "backup_text_extraction",
                "content_availability_status": "content_extracted",
                "fallback_method_used": True,
                "text_extraction_used": True,
                "insufficient_data": False
            }
            
            self.logger.info("🎉 Backup flow completed successfully!", {
                "processed_urls": len(processed_urls),
                "extracted_texts_count": len(extracted_texts),
                "backup_method": "text_extraction_from_top_3_urls",
                "classified_categories": len([k for k, v in classified_urls.items() if v]),
                "flow_indicators": self.backup_flow_indicators
            })
            
            return classified_urls, priority_urls, classified_urls
            
        except Exception as e:
            self.logger.error(f"❌ Backup flow failed with error: {str(e)}", {
                "error_type": type(e).__name__,
                "traceback": traceback.format_exc()
            })
            return None, None, None

    async def handle_insufficient_urls(self, website: str) -> Dict:
        """
        Handle case when parsed URLs are ≤1 by setting MCC to -1 and updating database
        
        Args:
            website (str): Website URL
            
        Returns:
            Dict: Analysis results with insufficient data values
        """
        start_time = time.time()
        
        try:
            # Create insufficient data values
            website_info = {
                "business_description": "insufficient data",
                "product_services": "insufficient data",
                "line_of_business": "insufficient data",
                "customers": "insufficient data",
                "website_description": "insufficient data"
            }
            
            mcc_info = {
                "mcc": -1,
                "reasoning": "insufficient data",
                "business_category": "insufficient data"
            }
            
            classified_urls = {}
            
            # Flow indicators for insufficient data
            flow_indicators = {
                "analysis_flow_type": "insufficient_data_flow",
                "content_availability_status": "content_insufficient",
                "fallback_method_used": True,
                "text_extraction_used": False,
                "insufficient_data": True
            }
            
            # Save results to database
            analysis_id = await self.save_mcc_analysis_results(
                website, classified_urls, website_info, mcc_info, flow_indicators
            )
            
            # Send webhook notification
            self.send_insufficient_data_webhook(website, analysis_id)
            
            processing_time = time.time() - start_time
            
            result = {
                "status": "COMPLETED",
                "analysis_id": analysis_id,
                "website": website,
                "scrape_request_ref_id": self.scrape_request_ref_id,
                "website_info": website_info,
                "mcc_info": mcc_info,
                "classified_urls": classified_urls,
                "priority_urls": {},
                "reasoning": "insufficient data",
                "processing_time": processing_time,
                "org_id": self.org_id,
                # Flow indicators
                "analysis_flow_type": "insufficient_data_flow",
                "content_availability_status": "content_insufficient",
                "fallback_method_used": True,
                "text_extraction_used": False,
                "insufficient_data": True
            }
            
            self.logger.info(
                "MCC analysis completed with insufficient data",
                {
                    "analysis_id": analysis_id,
                    "processing_time": f"{processing_time:.2f}s",
                    "mcc": -1
                }
            )
            
            return result
            
        except Exception as e:
            self.logger.error("Error handling insufficient URLs", error=e)
            await self.handle_analysis_error(e, "insufficient_urls")
            processing_time = time.time() - start_time
            
            return {
                "status": "FAILED",
                "error": f"Error handling insufficient URLs: {str(e)}",
                "website": website,
                "scrape_request_ref_id": self.scrape_request_ref_id,
                "processing_time": processing_time,
                "org_id": self.org_id,
                # Flow indicators for failed insufficient data handling
                "analysis_flow_type": "insufficient_data_error",
                "content_availability_status": "analysis_error",
                "fallback_method_used": False,
                "text_extraction_used": False,
                "insufficient_data": True
            }

    async def handle_site_unreachable(self, website: str) -> Dict:
        """
        Handle case when all URLs are unreachable by extracting home page text, 
        summarizing it, and continuing with MCC analysis
        
        Args:
            website (str): Website URL
            
        Returns:
            Dict: Analysis results after text extraction and summarization
        """
        start_time = time.time()
        
        try:
            self.logger.info(
                "[FALLBACK FLOW] Starting unreachable URLs backup flow with text extraction",
                {
                    "website": website,
                    "scrape_ref": self.scrape_request_ref_id,
                    "flow": "site_unreachable_fallback",
                    "strategy": "text_extraction_attempt",
                    "time": datetime.now().isoformat()
                }
            )
            
            # Step 1: Extract text from home page with fallback methods
            self.logger.info("Attempting text extraction from home page with multiple fallback methods")
            
            combined_text = await self.extract_home_page_text(website)
            
            if not combined_text or len(combined_text.strip()) < 50:
                self.logger.warning("Failed to extract meaningful text from any priority pages", {
                    "text_length": len(combined_text) if combined_text else 0,
                    "website": website,
                    "scrape_ref": self.scrape_request_ref_id,
                    "next_step": "handle_original_site_unreachable"
                })
                # Add debug log to trace flow
                self.logger.info("DEBUG - Text extraction fallback traced", {
                    "flow": "text_extraction_failed",
                    "time": datetime.now().isoformat()
                })
                # Fallback to original site unreachable behavior
                return await self.handle_original_site_unreachable(website)
            
            # Step 2: Crop combined text to <60K words if needed
            cropped_text = self.crop_text_to_limit(combined_text, 60000)
            
            # Step 3: Summarize the website content
            website_summary = self.summarize_website_content(cropped_text, website)
            
            if not website_summary:
                self.logger.warning("Failed to summarize website content", {
                    "website": website,
                    "scrape_ref": self.scrape_request_ref_id,
                    "text_length": len(cropped_text) if cropped_text else 0,
                    "next_step": "handle_original_site_unreachable"
                })
                # Add debug log to trace flow
                self.logger.info("DEBUG - Website summarization failed", {
                    "flow": "summarization_failed",
                    "time": datetime.now().isoformat()
                })
                # Fallback to original site unreachable behavior
                return await self.handle_original_site_unreachable(website)
            
            # Check if site is non-operational (new logic)
            if website_summary.get("site_non_operational", False):
                self.logger.info("Site detected as non-operational, returning MCC -1", {
                    "website": website,
                    "reason": website_summary.get("website_description", "Site not available")
                })
                
                # Create MCC info for non-operational site
                mcc_info = {
                    "mcc": -1,
                    "reasoning": website_summary.get("website_description", "Site not available"),
                    "business_category": "Site not available",
                    "business_desc": website_summary.get("website_description", "Site not available")
                }
                
                # Flow indicators for non-operational site
                flow_indicators = {
                    "analysis_flow_type": "site_non_operational_detected",
                    "content_availability_status": "content_non_operational",
                    "fallback_method_used": True,
                    "text_extraction_used": True,
                    "insufficient_data": False
                }
                
                # Save results to database
                analysis_id = await self.save_mcc_analysis_results(
                    website, {}, website_summary, mcc_info, flow_indicators
                )
                
                processing_time = time.time() - start_time
                
                result = {
                    "status": "COMPLETED",
                    "analysis_id": analysis_id,
                    "website": website,
                    "scrape_request_ref_id": self.scrape_request_ref_id,
                    "website_info": website_summary,
                    "mcc_info": mcc_info,
                    "classified_urls": {},
                    "priority_urls": {"home_page": [website]},
                    "reasoning": mcc_info.get("reasoning", ""),
                    "processing_time": processing_time,
                    "org_id": self.org_id,
                    # Flow indicators
                    "analysis_flow_type": "site_non_operational_detected",
                    "content_availability_status": "content_non_operational",
                    "fallback_method_used": True,
                    "text_extraction_used": True,
                    "insufficient_data": False
                }
                
                self.logger.info(
                    "MCC analysis completed - non-operational site detected",
                    {
                        "analysis_id": analysis_id,
                        "processing_time": f"{processing_time:.2f}s",
                        "mcc": -1,
                        "reason": mcc_info.get("reasoning", "")
                    }
                )
                
                return result
            
            # Step 4: Continue with MCC analysis using the summary (for operational sites)
            mcc_info = self.classify_mcc_from_summary(website, website_summary)
            
            if not mcc_info:
                self.logger.warning("MCC classification from summary failed")
                # Fallback to original site unreachable behavior
                return await self.handle_original_site_unreachable(website)
            
            # Flow indicators for fallback text extraction
            flow_indicators = {
                "analysis_flow_type": "fallback_text_extraction",
                "content_availability_status": "content_limited",
                "fallback_method_used": True,
                "text_extraction_used": True,
                "insufficient_data": False
            }
            
            # Step 5: Save results to database
            analysis_id = await self.save_mcc_analysis_results(
                website, {}, website_summary, mcc_info, flow_indicators
            )
            
            processing_time = time.time() - start_time
            
            result = {
                "status": "COMPLETED",
                "analysis_id": analysis_id,
                "website": website,
                "scrape_request_ref_id": self.scrape_request_ref_id,
                "website_info": website_summary,
                "mcc_info": mcc_info,
                "classified_urls": {},
                "priority_urls": {"home_page": [website]},
                "reasoning": mcc_info.get("reason", mcc_info.get("reasoning", "")),
                "processing_time": processing_time,
                "org_id": self.org_id,
                # Flow indicators
                "analysis_flow_type": "fallback_text_extraction",
                "content_availability_status": "content_limited",
                "fallback_method_used": True,
                "text_extraction_used": True,
                "insufficient_data": False
            }
            
            # Log detailed MCC mapping information
            self.logger.info(
                "MCC analysis completed using text extraction fallback",
                {
                    "analysis_id": analysis_id,
                    "processing_time": f"{processing_time:.2f}s",
                    "mcc_code": mcc_info.get("mcc", -1),
                    "business_category": mcc_info.get("business_category", "Unknown"),
                    "business_desc": mcc_info.get("business_desc", "Unknown"),
                    "reasoning": mcc_info.get("reason", mcc_info.get("reasoning", "No reasoning provided")),
                    "text_length": len(cropped_text),
                    "fallback_method": "text_extraction"
                }
            )
            
            return result
            
        except Exception as e:
            self.logger.error("Error in site unreachable fallback flow", error=e)
            # Fallback to original site unreachable behavior
            return await self.handle_original_site_unreachable(website)

    def clean_url_to_main_domain(self, url: str) -> str:
        """
        Clean URL to extract the main domain for fallback text extraction
        
        Args:
            url (str): Original URL (might be a sub-page)
            
        Returns:
            str: Cleaned main domain URL
        """
        try:
            from urllib.parse import urlparse
            
            if not url or not isinstance(url, str):
                return url
            
            # Parse the URL
            parsed = urlparse(url.strip())
            
            # Handle cases where scheme is missing
            if not parsed.scheme:
                url = "https://" + url.strip()
                parsed = urlparse(url)
            
            # Extract main domain components
            scheme = parsed.scheme if parsed.scheme else "https"
            netloc = parsed.netloc if parsed.netloc else parsed.path.split('/')[0]
            
            # Clean the netloc to remove any port numbers or extra components
            if ':' in netloc and not netloc.startswith('['):  # Avoid IPv6 addresses
                netloc = netloc.split(':')[0]
            
            # Construct the main domain URL (root path)
            main_domain = f"{scheme}://{netloc}/"
            
            self.logger.info("URL cleaned to main domain", {
                "original_url": url,
                "main_domain": main_domain,
                "was_subpage": url != main_domain
            })
            
            return main_domain
            
        except Exception as e:
            self.logger.warning("Error cleaning URL to main domain", {"url": url, "error": str(e)})
            return url  # Return original if cleaning fails

    def get_url_fallback_candidates(self, website: str) -> List[str]:
        """
        Generate a list of URL candidates to try for text extraction
        
        Args:
            website (str): Original website URL
            
        Returns:
            List[str]: List of URLs to try in order of preference
        """
        candidates = []
        
        try:
            # 1. Original URL (as provided)
            if website:
                candidates.append(website)
            
            # 2. Main domain version
            main_domain = self.clean_url_to_main_domain(website)
            if main_domain and main_domain != website:
                candidates.append(main_domain)
            
            # 3. Alternative protocol versions
            from urllib.parse import urlparse
            parsed = urlparse(website)
            
            if parsed.scheme == "https":
                # Try HTTP version of the original URL
                http_version = website.replace("https://", "http://", 1)
                if http_version not in candidates:
                    candidates.append(http_version)
                
                # Try HTTP version of main domain
                http_main = main_domain.replace("https://", "http://", 1)
                if http_main not in candidates:
                    candidates.append(http_main)
            
            elif parsed.scheme == "http":
                # Try HTTPS version of the original URL
                https_version = website.replace("http://", "https://", 1)
                if https_version not in candidates:
                    candidates.append(https_version)
                
                # Try HTTPS version of main domain
                https_main = main_domain.replace("http://", "https://", 1)
                if https_main not in candidates:
                    candidates.append(https_main)
            
            # Remove duplicates while preserving order
            unique_candidates = []
            seen = set()
            for candidate in candidates:
                if candidate not in seen:
                    unique_candidates.append(candidate)
                    seen.add(candidate)
            
            self.logger.info("Generated URL fallback candidates", {
                "original_url": website,
                "total_candidates": len(unique_candidates),
                "candidates": unique_candidates
            })
            
            return unique_candidates
            
        except Exception as e:
            self.logger.warning("Error generating URL fallback candidates", {"website": website, "error": str(e)})
            return [website] if website else []

    async def extract_text_with_method(self, url: str, method: str) -> str:
        """
        Extract text using a specific method

        Args:
            url (str): URL to extract text from
            method (str): Method to use ('playwright' or 'requests')

        Returns:
            str: Extracted text or empty string if failed
        """
        try:
            if method == "playwright":
                from app.services.screenshot.url_utils import get_text_from_url_local

                self.logger.info(f"Extracting text using Playwright for: {url}")
                extracted_text = await get_text_from_url_local(
                    url,
                    endpoint_type="mcc_analysis",
                    org_id=self.org_id,
                    timeout=45  # 45 second timeout for faster processing
                )
                return extracted_text if extracted_text else ""
                    
            elif method == "requests":
                import requests
                from bs4 import BeautifulSoup
                
                headers = {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
                }
                response = requests.get(url, headers=headers, timeout=20, allow_redirects=True)
                
                if response.status_code == 200:
                    soup = BeautifulSoup(response.content, 'html.parser')
                    
                    # Remove script and style elements
                    for script in soup(["script", "style"]):
                        script.decompose()
                    
                    # Extract text
                    text = soup.get_text()
                    # Clean up text
                    lines = (line.strip() for line in text.splitlines())
                    chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
                    text = ' '.join(chunk for chunk in chunks if chunk)
                    
                    return text if text else ""
                else:
                    self.logger.warning(f"HTTP request failed with status {response.status_code}", {"url": url})
                    return ""
            
            return ""
            
        except Exception as e:
            self.logger.warning(f"Text extraction method '{method}' failed", {"url": url, "error": str(e)})
            return ""

    async def extract_home_page_text(self, website: str) -> str:
        """
        Extract text content from the home page using multiple URL and method fallbacks
        
        Args:
            website (str): Website URL (might be a sub-page)
            
        Returns:
            str: Extracted text content or empty string if failed
        """
        try:
            self.logger.info("Starting robust text extraction with URL cleanup", {"website": website})
            
            # Get all URL candidates (original URL, main domain, protocol variants)
            url_candidates = self.get_url_fallback_candidates(website)
            extraction_methods = ["playwright", "requests"]
            
            # Try each URL candidate with each extraction method
            for url_index, url in enumerate(url_candidates):
                self.logger.info(f"Trying URL candidate {url_index + 1}/{len(url_candidates)}", {"url": url})
                
                for method_index, method in enumerate(extraction_methods):
                    try:
                        self.logger.info(f"Attempting {method} extraction", {"url": url, "method": method})
                        
                        extracted_text = await self.extract_text_with_method(url, method)
                        
                        if extracted_text and len(extracted_text.strip()) > 100:
                            self.logger.info(f"✅ Text extraction successful with {method}", {
                                "url": url,
                                "method": method,
                                "text_length": len(extracted_text),
                                "url_candidate": url_index + 1,
                                "method_attempt": method_index + 1,
                                "is_main_domain": url != website
                            })
                            return extracted_text
                        else:
                            self.logger.warning(f"{method} extraction returned insufficient text", {
                                "url": url,
                                "text_length": len(extracted_text) if extracted_text else 0
                            })
                            
                    except Exception as method_error:
                        self.logger.warning(f"{method} extraction failed for URL", {
                            "url": url,
                            "method": method,
                            "error": str(method_error)
                        })
                        continue
            
            # If all URL candidates and methods fail
            self.logger.error("All text extraction methods and URL candidates failed", {
                "original_website": website,
                "total_candidates_tried": len(url_candidates),
                "total_methods_tried": len(extraction_methods)
            })
            return ""
                
        except Exception as e:
            self.logger.error("Critical error in extract_home_page_text", {"website": website, "error": str(e)})
            return ""

    def crop_text_to_limit(self, text: str, word_limit: int = 60000) -> str:
        """
        Crop text to specified word limit
        
        Args:
            text (str): Input text
            word_limit (int): Maximum number of words (default 60,000)
            
        Returns:
            str: Cropped text
        """
        try:
            if not text:
                return ""
            
            words = text.split()
            if len(words) <= word_limit:
                self.logger.info(f"Text is within limit: {len(words)} words")
                return text
            
            cropped_text = " ".join(words[:word_limit])
            self.logger.info(
                f"Text cropped from {len(words)} to {word_limit} words",
                {"original_chars": len(text), "cropped_chars": len(cropped_text)}
            )
            return cropped_text
            
        except Exception as e:
            self.logger.error("Error cropping text", error=e)
            return text  # Return original text if cropping fails

    def summarize_website_content(self, text: str, website: str) -> Optional[Dict]:
        """
        Summarize website content using Gemini with comprehensive edge case handling
        """
        try:
            # Input validation
            if not text or len(text.strip()) < 10:
                self.logger.error("Text too short for summarization", {"text_length": len(text) if text else 0})
                return None
            if not website:
                self.logger.error("Website URL is required for summarization")
                return None
            from app.gpt_models.gpt_prompts import GptPromptPicker
            # --- TRUNCATE TEXT BEFORE PROMPT ---
            truncated_text = self.truncate_text_for_gemini(text, max_chars=12000)  # ~6-8k tokens for most languages
            if len(truncated_text) < len(text):
                self.logger.warning(f"Extracted text truncated from {len(text)} to {len(truncated_text)} characters for Gemini input.")
            self.logger.info("Summarizing website content using Gemini", {"text_length": len(truncated_text), "website": website})
            try:
                # Create the summarization prompt
                prompt = GptPromptPicker.summarize_website(truncated_text, "")
                if not prompt or len(prompt.strip()) < 10:
                    self.logger.error("Failed to create valid summarization prompt")
                    return None
            except Exception as prompt_error:
                self.logger.error("Error creating summarization prompt", error=prompt_error)
                return None
            # Get response from Gemini with comprehensive error handling
            max_retries = 3
            for attempt in range(max_retries):
                try:
                    from app.gpt_models.gemini_model_wrapper.gemeni_utils import get_gemini_response_mcc_analysis
                    import time
                    start_time = time.time()
                    self.logger.info(f"Gemini summarization attempt {attempt + 1}/{max_retries} - BACKUP FLOW", {"prompt_length": len(prompt)})
                    response = get_gemini_response_mcc_analysis(
                        prompt,
                        model_name="gemini-2.5-flash",
                        timeout_seconds=120  # Increased timeout for backup flow summarization
                    )
                    elapsed = time.time() - start_time
                    self.logger.info(f"Gemini summarization call took {elapsed:.2f} seconds", {"attempt": attempt + 1})
                    if not response:
                        self.logger.warning(f"Empty response from Gemini on attempt {attempt + 1}")
                        continue
                    # Parse the JSON response from Gemini
                    summary = self.parse_json_response(response)
                    if summary and isinstance(summary, dict):
                        # Check if site is non-operational (updated logic for new 5-key format)
                        non_operational = summary.get("non_operational", "no")
                        if str(non_operational).lower() == "yes":
                            self.logger.info("Site detected as non-operational", {
                                "website": website,
                                "reason": summary.get("website_description", "Site not available"),
                                "non_operational_status": non_operational
                            })
                            # Return special indicator for non-operational sites with updated structure
                            return {
                                "non_operational": "yes",
                                "product_services": summary.get("product_services", "Site not available"),
                                "line_of_business": summary.get("line_of_business", "Site not available"),
                                "customers": summary.get("customers", "Site not available"),
                                "website_description": summary.get("website_description", "Site not available"),
                                "site_non_operational": True  # Internal flag for backward compatibility
                            }
                        
                        # Validate summary has required fields for operational sites (updated for 5-key format)
                        required_fields = ["non_operational", "product_services", "line_of_business", "customers", "website_description"]
                        missing_fields = [field for field in required_fields if field not in summary or not summary[field]]
                        
                        if missing_fields:
                            self.logger.warning(f"Summary missing required fields: {missing_fields}")
                            # If only a few fields are missing, continue with what we have
                            if len(missing_fields) < len(required_fields):
                                self.logger.info("Using partial summary with available fields", {"available_fields": list(summary.keys())})
                                # Add the site_non_operational flag for internal processing
                                if "site_non_operational" not in summary:
                                    summary["site_non_operational"] = False
                                return summary
                        else:
                            self.logger.info("Website content summarized successfully", {"summary_fields": list(summary.keys())})
                            # Add the site_non_operational flag for internal processing
                            if "site_non_operational" not in summary:
                                summary["site_non_operational"] = False
                            return summary
                    else:
                        self.logger.warning(f"Invalid response format on attempt {attempt + 1}: {type(summary)}")
                    
                except Exception as api_error:
                    self.logger.error(f"Gemini API error on attempt {attempt + 1}", error=api_error)
                
                if attempt < max_retries - 1:
                    import time
                    delay = (attempt + 1) * 2  # Progressive delay: 2s, 4s, 6s
                    self.logger.warning(f"Attempt {attempt + 1} failed, retrying in {delay}s...")
                    time.sleep(delay)
            
            self.logger.error("All summarization attempts failed - unable to generate website summary")
            return None
            
        except Exception as e:
            self.logger.error("Critical error in website content summarization", error=e)
            return None

    def truncate_text_for_gemini(self, text, max_chars=12000):
        # Fallback: truncate by characters (safe for most cases, but not exact tokens)
        if len(text) > max_chars:
            return text[:max_chars]
        return text

    def create_website_info_prompt_with_content(self, website: str, extracted_content: Dict) -> str:
        """
        Create a website information prompt using extracted text content

        Args:
            website (str): Website URL
            extracted_content (Dict): Dictionary with extracted text content by category

        Returns:
            str: Formatted prompt for Gemini analysis
        """
        try:
            # Build content sections
            content_sections = []

            for category, content_data in extracted_content.items():
                if isinstance(content_data, dict) and "text" in content_data and "url" in content_data:
                    section = f"""
{category.upper().replace('_', ' ')} CONTENT:
URL: {content_data['url']}
Content: {content_data['text'][:3000]}  # Limit to 3000 chars per section
"""
                    content_sections.append(section)

            # Combine all content
            all_content = "\n".join(content_sections)

            # Create the prompt
            prompt = f"""
You are an expert in analyzing website content to extract business information. Based on the extracted content from the website {website}, please analyze and provide the following information:

EXTRACTED WEBSITE CONTENT:
{all_content}

Please analyze the above content and provide the following information in JSON format:

1. **product_services**: What products or services does this business offer? Be specific and comprehensive.
2. **line_of_business**: What is the primary business category or industry?
3. **customers**: Who are the target customers (B2B, B2C, specific demographics)?
4. **website_description**: A brief description of what this website/business is about.

Important guidelines:
- Base your analysis ONLY on the provided content
- Be specific and detailed in your responses
- If information is not clear from the content, indicate "insufficient data" for that field
- Do not make assumptions beyond what's explicitly stated in the content

Output format (JSON only, no additional text):
{{
    "is_valid_website": "yes",
    "website_redirection": "no",
    "product_services": "<detailed description>",
    "line_of_business": "<business category>",
    "customers": "<target customer description>",
    "website_description": "<website description>"
}}
"""

            return prompt

        except Exception as e:
            self.logger.error("Error creating website info prompt with content", {"error": str(e)})
            # Fallback to basic prompt
            return f"""
Analyze the website {website} and provide business information in JSON format:
{{
    "is_valid_website": "yes",
    "website_redirection": "no",
    "product_services": "Unable to determine",
    "line_of_business": "General business",
    "customers": "General customers",
    "website_description": "Website analysis failed"
}}
"""

    def classify_mcc_from_summary(self, website: str, website_summary: Dict) -> Optional[Dict]:
        """
        Classify MCC using the website summary
        
        Args:
            website (str): Website URL
            website_summary (Dict): Summarized website information
            
        Returns:
            Optional[Dict]: MCC classification information
        """
        try:
            # Input validation
            if not website_summary or not isinstance(website_summary, dict):
                self.logger.error("Invalid website summary for MCC classification", {"summary_type": type(website_summary)})
                return None
                
            if not website:
                self.logger.error("Website URL is required for MCC classification")
                return None
            
            from app.gpt_models.gpt_prompts import GptPromptPicker
            import json
            import pandas as pd
            
            self.logger.info("Classifying MCC from website summary", {"summary_fields": list(website_summary.keys())})
            
            # Check if this is a non-operational site (updated for 5-key format + backward compatibility)
            is_non_operational = (
                website_summary.get("site_non_operational", False) or  # Backward compatibility
                str(website_summary.get("non_operational", "no")).lower() == "yes"  # New format
            )
            
            if is_non_operational:
                self.logger.info("Non-operational site detected during MCC classification, returning -1", {
                    "non_operational_flag": website_summary.get("non_operational", "unknown"),
                    "site_non_operational_flag": website_summary.get("site_non_operational", False)
                })
                return {
                    "mcc": -1,
                    "reasoning": website_summary.get("website_description", "Site not available"),
                    "business_category": "Site not available",
                    "business_desc": website_summary.get("website_description", "Site not available")
                }
            
            # Check for "Site not available" indicators in the summary
            site_not_available_indicators = [
                "site not available", "site is not reachable", "coming soon", 
                "under construction", "page not found", "domain expired",
                "default template", "not operational", "not available"
            ]
            
            website_desc = website_summary.get("website_description", "").lower()
            product_services = website_summary.get("product_services", "").lower()
            
            if any(indicator in website_desc or indicator in product_services for indicator in site_not_available_indicators):
                self.logger.info("Non-operational site indicators detected, returning MCC -1", {
                    "website_description": website_summary.get("website_description", ""),
                    "product_services": website_summary.get("product_services", "")
                })
                return {
                    "mcc": -1,
                    "reasoning": website_summary.get("website_description", "Site not available"),
                    "business_category": "Site not available", 
                    "business_desc": website_summary.get("website_description", "Site not available")
                }
            
            # Load MCC data for classification with error handling
            mcc_data_path = "app/input/combined_mcc_data(1).csv"
            special_mcc_path = "app/input/special_mccs.csv"
            visa_data_path = "app/input/combined_mcc_data(1).csv"  # Use the same file for visa data
            
            try:
                mcc_df = pd.read_csv(mcc_data_path)
                special_mcc_df = pd.read_csv(special_mcc_path)
                visa_df = pd.read_csv(visa_data_path)
                
                self.logger.info("MCC data files loaded successfully", {
                    "mcc_records": len(mcc_df),
                    "special_mcc_records": len(special_mcc_df),
                    "visa_records": len(visa_df)
                })
                
            except Exception as data_error:
                self.logger.error("Error loading MCC data files", error=data_error)
                return None
            
            # Create prompt for MCC classification using the summary
            max_retries = 3
            for attempt in range(max_retries):
                try:
                    # Use the existing MCC classification prompt with summary data
                    prompt = GptPromptPicker.get_website_mcc_classifier_prompt_full(
                        mcc_info=mcc_df.to_dict('records'),
                        excluded_mccs=[],
                        website_info=website_summary,
                        filtered_special_mcc=special_mcc_df.to_dict('records'),
                        filtered_visa_data=visa_df.to_dict('records'),
                        website_url=website
                    )
                    
                    if not prompt:
                        self.logger.error("Failed to create MCC classification prompt")
                        continue
                    
                    self.logger.info(f"MCC classification attempt {attempt + 1}/{max_retries} - BACKUP FLOW", {"prompt_length": len(prompt)})
                    
                    # Get response from Gemini
                    from app.gpt_models.gemini_model_wrapper.gemeni_utils import get_gemini_response_mcc_analysis
                    
                    response = get_gemini_response_mcc_analysis(
                        prompt,
                        model_name="gemini-2.5-flash",
                        timeout_seconds=120  # Increased timeout for backup flow MCC classification
                    )
                    
                    if not response:
                        self.logger.warning(f"Empty response from Gemini on attempt {attempt + 1}")
                        continue
                    
                    # Parse the JSON response
                    mcc_result = self.parse_json_response(response)
                    if mcc_result and isinstance(mcc_result, dict):
                        # Validate MCC result
                        required_fields = ["mcc", "business_desc", "business_category"]
                        missing_fields = [field for field in required_fields if field not in mcc_result]
                        
                        if missing_fields:
                            self.logger.warning(f"MCC result missing fields: {missing_fields}")
                        
                        # Check if we got a valid MCC code
                        mcc_code = mcc_result.get("mcc", -1)
                        if mcc_code == -1:
                            self.logger.warning("Got MCC -1 for operational site, applying default handling")
                            mcc_result["mcc"] = "-1"
                            mcc_result["business_desc"] = "Site not available"
                            mcc_result["business_category"] = "Site not available"
                        
                        self.logger.info("MCC classification from summary successful", {
                            "mcc_code": mcc_result.get("mcc", -1),
                            "business_category": mcc_result.get("business_category", "Unknown"),
                            "has_reasoning": bool(mcc_result.get("reason") or mcc_result.get("reasoning"))
                        })
                        return mcc_result
                    else:
                        self.logger.warning(f"Invalid MCC result format on attempt {attempt + 1}: {type(mcc_result)}")
                    
                except Exception as attempt_error:
                    self.logger.error(f"MCC classification attempt {attempt + 1} failed", error=attempt_error)
                
                if attempt < max_retries - 1:
                    import time
                    delay = (attempt + 1) * 2  # Progressive delay
                    self.logger.warning(f"Attempt {attempt + 1} failed, retrying in {delay}s...")
                    time.sleep(delay)
            
            self.logger.error("All MCC classification attempts failed")
            return None
            
        except Exception as e:
            self.logger.error("Critical error in MCC classification from summary", error=e)
            return None

    async def handle_original_site_unreachable(self, website: str) -> Dict:
        """
        Original site unreachable handler - fallback when text extraction fails
        
        Args:
            website (str): Website URL
            
        Returns:
            Dict: Analysis results with site unreachable values
        """
        start_time = time.time()
        
        try:
            # Create site unreachable values
            website_info = {
                "business_description": "site is not reachable",
                "product_services": "site is not reachable",
                "line_of_business": "site is not reachable", 
                "customers": "site is not reachable",
                "website_description": "site is not reachable"
            }
            
            mcc_info = {
                "mcc": -1,
                "reasoning": "All URLs are not reachable - site is not accessible",
                "business_category": "site not reachable"
            }
            
            classified_urls = {}
            
            # Flow indicators for site unreachable
            flow_indicators = {
                "analysis_flow_type": "site_unreachable_fallback",
                "content_availability_status": "content_unavailable",
                "fallback_method_used": True,
                "text_extraction_used": False,
                "insufficient_data": False
            }
            
            # Save results to database
            analysis_id = await self.save_mcc_analysis_results(
                website, classified_urls, website_info, mcc_info, flow_indicators
            )
            
            # Send webhook notification for site unreachable
            self.send_site_unreachable_webhook(website, analysis_id)
            
            processing_time = time.time() - start_time
            
            result = {
                "status": "COMPLETED",
                "analysis_id": analysis_id,
                "website": website,
                "scrape_request_ref_id": self.scrape_request_ref_id,
                "website_info": website_info,
                "mcc_info": mcc_info,
                "classified_urls": classified_urls,
                "priority_urls": {},
                "reasoning": "All URLs are not reachable - site is not accessible",
                "processing_time": processing_time,
                "org_id": self.org_id,
                # Flow indicators
                "analysis_flow_type": "site_unreachable_fallback",
                "content_availability_status": "content_unavailable",
                "fallback_method_used": True,
                "text_extraction_used": False,
                "insufficient_data": False
            }
            
            self.logger.info(
                "[FINAL FALLBACK] MCC analysis completed with final site unreachable status",
                {
                    "analysis_id": analysis_id,
                    "processing_time": f"{processing_time:.2f}s",
                    "mcc": -1,
                    "reason": "All URLs not reachable",
                    "website": website,
                    "scrape_ref": self.scrape_request_ref_id,
                    "flow": "FINAL_FALLBACK_COMPLETE",
                    "time": datetime.now().isoformat()
                }
            )
            
            return result
            
        except Exception as e:
            self.logger.error("Error handling original site unreachable", error=e)
            await self.handle_analysis_error(e, "site_unreachable")
            processing_time = time.time() - start_time
            
            return {
                "status": "FAILED",
                "error": f"Error handling site unreachable: {str(e)}",
                "website": website,
                "scrape_request_ref_id": self.scrape_request_ref_id,
                "processing_time": processing_time,
                "org_id": self.org_id,
                # Flow indicators for failed site unreachable handling
                "analysis_flow_type": "site_unreachable_error",
                "content_availability_status": "analysis_error",
                "fallback_method_used": False,
                "text_extraction_used": False,
                "insufficient_data": False
            }

    def send_site_unreachable_webhook(self, website: str, analysis_id: int) -> None:
        """
        Send webhook notification for site unreachable case
        
        Args:
            website (str): Website URL
            analysis_id (int): Analysis ID
        """
        try:
            from app.utils.webhook_utils import send_webhook_notification
            from app.config import settings
            
            webhook_data = {
                "analysis_id": analysis_id,
                "website": website,
                "status": "COMPLETED",
                "mcc": -1,
                "reason": "All URLs are not reachable - site is not accessible",
                "org_id": self.org_id,
                "scrape_request_ref_id": self.scrape_request_ref_id
            }
            
            if hasattr(settings, 'WEBHOOK_URL') and settings.WEBHOOK_URL:
                send_webhook_notification(settings.WEBHOOK_URL, webhook_data, self.logger)
            else:
                self.logger.info("No webhook URL configured - skipping webhook notification")
                
        except Exception as e:
            self.logger.error("Error sending site unreachable webhook", error=e)

    def send_insufficient_data_webhook(self, website: str, analysis_id: int) -> None:
        """
        Send webhook notification for insufficient data case
        
        Args:
            website (str): Website URL
            analysis_id (int): Analysis ID
        """
        try:
            from app.utils.webhook_utils import send_webhook_notification
            from app.config import settings
            
            # Prepare webhook data
            webhook_data = {
                "scrape_request_ref_id": self.scrape_request_ref_id,
                "mcc": -1,
                "businessCategory": "insufficient data",
                "businessDescription": "insufficient data",
                "status": "COMPLETED",
                "reasoning": "insufficient data"
            }
            
            # Send MCC webhook
            mcc_webhook_url = f"{settings.BASE_URL}/api/mcc/results"
            success = send_webhook_notification(
                mcc_webhook_url, 
                webhook_data, 
                "MCC_INSUFFICIENT_DATA", 
                self.logger
            )
            
            if success:
                self.logger.info("Insufficient data webhook sent successfully")
            else:
                self.logger.error("Failed to send insufficient data webhook")
                
        except Exception as e:
            self.logger.error("Error sending insufficient data webhook", error=e)


# Factory function for creating MCC classification service
def create_mcc_classification_service(scrape_request_ref_id: str, org_id: str = "default") -> MccClassificationService:
    return MccClassificationService(scrape_request_ref_id, org_id)