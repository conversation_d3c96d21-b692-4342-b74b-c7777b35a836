"""
New URL Processing Service implementing the simplified flow:
1. Get All extracted URLs
2. Dedupe all the URLs  
3. Sort the URLs in ascending order of the length and take top 100
4. Send 10 URLs at once and find out how many are reachable, where the content can be read by Gemini
5. Iterate until x amount of reachable URLs reached, for now x can be 4
6. Fetch home page URL from the soft classifier
7. Dedupe the final list of reachable URL+home page URL (4+1 URLs)
8. Pass just once 1 URLs at one time, therefore total maximum 5 calls
"""

import asyncio
from typing import List, Dict, Set, Tuple
from urllib.parse import urlparse
import logging
from app.utils.logger import ConsoleLogger
from app.gpt_models.gpt_prompts import GptPromptPicker
from app.gpt_models.gemini_model_wrapper.gemeni_utils import get_gemini_response_mcc_analysis


class UrlProcessor:
    def __init__(self, logger: ConsoleLogger = None):
        self.logger = logger or ConsoleLogger("url_processor")
        
    def extract_all_urls(self, parsed_urls: Dict) -> List[str]:
        """Step 1: Extract all URLs from the parsed_urls structure"""
        all_urls = []
        
        try:
            # Extract URLs from depth 1 and depth 2
            if 'depth_1' in parsed_urls:
                all_urls.extend(parsed_urls['depth_1'])
                
            if 'depth_2' in parsed_urls:
                all_urls.extend(parsed_urls['depth_2'])
                
            self.logger.info(f"Extracted {len(all_urls)} total URLs")
            return all_urls
            
        except Exception as e:
            self.logger.error(f"Error extracting URLs: {str(e)}")
            return []
    
    def dedupe_urls(self, urls: List[str]) -> List[str]:
        """Step 2: Remove duplicate URLs"""
        try:
            # Use set to remove duplicates while preserving order
            seen = set()
            deduped = []
            
            for url in urls:
                if url and url not in seen:
                    seen.add(url)
                    deduped.append(url)
            
            self.logger.info(f"Deduped URLs: {len(urls)} -> {len(deduped)}")
            return deduped
            
        except Exception as e:
            self.logger.error(f"Error deduping URLs: {str(e)}")
            return urls
    
    def sort_and_limit_urls(self, urls: List[str], limit: int = 100) -> List[str]:
        """Step 3: Sort URLs by length (ascending) and take top 100"""
        try:
            # Sort by URL length (shortest first)
            sorted_urls = sorted(urls, key=len)
            
            # Limit to top 100
            limited_urls = sorted_urls[:limit]
            
            self.logger.info(f"Sorted and limited URLs: {len(urls)} -> {len(limited_urls)}")
            return limited_urls
            
        except Exception as e:
            self.logger.error(f"Error sorting and limiting URLs: {str(e)}")
            return urls[:limit]
    
    async def check_url_reachability_batch(self, urls: List[str]) -> List[str]:
        """Check if URLs are reachable by Gemini in batches of 10"""
        reachable_urls = []
        
        try:
            # Create a simple prompt to test URL reachability
            test_prompt = """
            Please visit the provided URLs and check if they are reachable and contain readable content.
            Return a JSON response with the following format:
            {
                "reachable_urls": ["list of URLs that are reachable and have readable content"]
            }
            """
            
            # Format URLs for the prompt
            urls_text = "\n".join([f"{i+1}. {url}" for i, url in enumerate(urls)])
            full_prompt = f"{test_prompt}\n\nURLs to check:\n{urls_text}"
            
            # Use Gemini to check reachability
            response = get_gemini_response_mcc_analysis(
                prompt=full_prompt,
                model_name="gemini-2.5-flash",
                timeout_seconds=60
            )
            
            if response and 'reachable_urls' in response:
                reachable_urls = response['reachable_urls']
                self.logger.info(f"Found {len(reachable_urls)} reachable URLs out of {len(urls)}")
            else:
                self.logger.warning("No reachable URLs found in response")
                
        except Exception as e:
            self.logger.error(f"Error checking URL reachability: {str(e)}")
            
        return reachable_urls
    
    async def find_reachable_urls(self, urls: List[str], target_count: int = 4) -> List[str]:
        """Steps 4-5: Find reachable URLs by checking in batches of 10 until target count is reached"""
        reachable_urls = []
        batch_size = 10
        
        try:
            for i in range(0, len(urls), batch_size):
                if len(reachable_urls) >= target_count:
                    break
                    
                batch = urls[i:i + batch_size]
                self.logger.info(f"Checking batch {i//batch_size + 1}: {len(batch)} URLs")
                
                batch_reachable = await self.check_url_reachability_batch(batch)
                reachable_urls.extend(batch_reachable)
                
                self.logger.info(f"Total reachable URLs so far: {len(reachable_urls)}")
            
            # Limit to target count
            final_reachable = reachable_urls[:target_count]
            self.logger.info(f"Final reachable URLs: {len(final_reachable)}")
            
            return final_reachable
            
        except Exception as e:
            self.logger.error(f"Error finding reachable URLs: {str(e)}")
            return reachable_urls[:target_count]
    
    async def get_home_page_url(self, website: str, all_urls: List[str]) -> str:
        """Step 6: Get home page URL using soft classifier"""
        try:
            # Use the home page identification prompt
            prompt = GptPromptPicker.get_home_page_url_prompt(website, all_urls)

            response = get_gemini_response_mcc_analysis(
                prompt=prompt,
                model_name="gemini-2.5-flash",
                timeout_seconds=60
            )
            
            if response and 'home_page_url' in response:
                home_page_url = response['home_page_url']
                self.logger.info(f"Identified home page URL: {home_page_url}")
                return home_page_url
            else:
                # Fallback: return the shortest URL as home page
                if all_urls:
                    home_page_url = min(all_urls, key=len)
                    self.logger.warning(f"Using fallback home page URL: {home_page_url}")
                    return home_page_url
                    
        except Exception as e:
            self.logger.error(f"Error getting home page URL: {str(e)}")
            
        return ""
    
    def create_final_url_list(self, reachable_urls: List[str], home_page_url: str) -> List[str]:
        """Step 7: Dedupe final list of reachable URLs + home page URL (max 5 URLs)"""
        try:
            # Combine reachable URLs with home page URL
            all_final_urls = reachable_urls.copy()
            
            if home_page_url and home_page_url not in all_final_urls:
                all_final_urls.append(home_page_url)
            
            # Dedupe again
            final_urls = list(dict.fromkeys(all_final_urls))  # Preserves order
            
            # Limit to 5 URLs maximum
            final_urls = final_urls[:5]
            
            self.logger.info(f"Final URL list created: {len(final_urls)} URLs")
            return final_urls
            
        except Exception as e:
            self.logger.error(f"Error creating final URL list: {str(e)}")
            return reachable_urls[:5]
    
    async def analyze_url_individually(self, website: str, url: str) -> Dict:
        """Step 8: Analyze each URL individually for website information"""
        try:
            # Use the website information prompt for single URL
            prompt = GptPromptPicker.get_website_information_prompt(website, url)

            response = get_gemini_response_mcc_analysis(
                prompt=prompt,
                model_name="gemini-2.5-flash",
                timeout_seconds=60
            )
            
            if response:
                self.logger.info(f"Successfully analyzed URL: {url}")
                return response
            else:
                self.logger.warning(f"No response for URL: {url}")
                return {}
                
        except Exception as e:
            self.logger.error(f"Error analyzing URL {url}: {str(e)}")
            return {}
