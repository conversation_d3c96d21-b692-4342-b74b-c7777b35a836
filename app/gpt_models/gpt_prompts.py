import json


class GptPromptPicker:
    @staticmethod
    def get_website_information_prompt(website, url):
        """Generate a prompt for extracting website information from a single URL"""

        prompt = f"""
        * Role * : You are an expert in analysing and extracting information from websites. The information you extract will be used to classify this website into an appropriate merchant category code (referred as MCC).

        * Task Starts *
        You will be given a website URL. Your task will be to visit this URL and extract the information such as:
        1. If the website is live and reachable
        2. If the website redirects to another website
        3. Distinct types of products and services this website sells or offers.
        4. Line of business of this website.
        5. Set of customers this website caters to.
        6. A short website description which can be very helpful for categorizing this business to a MCC.
        * Task Ends *

        * Input Starts *
        You are given following things:
        1. Website : {website}
        2. URL to analyze: {url}
        * Input Ends *

        * Guidelines *
        1. Verify the website URL and ensure it's from the provided website domain only.
        2. Check if the website is active and valid. It should NOT be unreachable OR have domain for sale.
        3. Extract all the products, services, their description mentioned in the website to come up with distinct types of products and services and line of business.
        4. Extract the line of business of this website by going through the URL content.
        5. Look at keywords in products and services first and then images for extracting the set of customers this website caters to, e.g. men, women, boys, girls, children, infants, general public, pets such as dogs, cats etc.

        * Things to keep in mind *
        1. Extract information from the website only using the provided URL - do not include if it navigates to another website or domain.
        2. Do not assume any information, if you are not sure about the information, do not include it in the output.
        3. Website's URL and its meaning may be misleading so tread carefully.
        4. Do not use your past knowledge regarding the website, extracted information should STRICTLY reflect realtime status of the website.
        5. While extracting customers refer explicit keywords first for example: men, women, boys, girls, children, infants, general public, pets such as dogs, cats etc and then refer images if keywords are not explicitly mentioned.

        Please note : Do not continue the analysis if the website is not valid or is redirecting to any other different domain name. Simply assign "is_valid_website" as "no" and "website_redirection" as "yes".

        * Output format starts *

        The output should strictly be in a json format with the below 6 keys and do not return text before or after.
        {{
        "is_valid_website" : <str> "yes" or "no", only one amongst yes and no if the site is not active
        "website_redirection": <str> "yes" or "no"
        "product_services": <str>, holistic types covering product and services,
        "line_of_business": <str>,
        "customers": <str>
        "website_description": <str>
        }}
        * Output format ends *
        """

        return prompt

    @staticmethod
    def get_website_mcc_classifier_prompt_full(website_info, filtered_special_mcc, filtered_visa_data, website_url=None):
        """Generate MCC classification prompt with comprehensive error handling"""
        try:
            # Validate and sanitize inputs
            if not website_info or not isinstance(website_info, dict):
                website_info = {
                    "product_services": "Unable to determine",
                    "line_of_business": "General business",
                    "customers": "General customers",
                    "website_description": "Website information not available"
                }

            if not isinstance(filtered_special_mcc, list):
                filtered_special_mcc = []

            if not isinstance(filtered_visa_data, list):
                filtered_visa_data = []

            if not website_url or not isinstance(website_url, str):
                website_url = "Unknown website"

        except Exception as e:
            # Use safe defaults without logging sensitive data
            website_info = {"product_services": "Error processing", "line_of_business": "Unknown", "customers": "Unknown", "website_description": "Error"}
            filtered_special_mcc = []
            filtered_visa_data = []
            website_url = "Error"

        prompt = f"""
        *** Persona ***
        * Persona *
            You are an expert "payment aggregator merchant onboarding specialist" with exceptional reasoning skills and deep knowledge of merchant category codes (MCCs).
            Your task is to accurately classify the business to a single correct MCC based on the all information provided to you.

            * Tasks Starts *
            1. Analyze the provided website information carefully on the lines of the MCC definitions given to you
            2. Match this business to the most specific and appropriate MCC from the provided MCC lists and Provide a clear, logical reason for your MCC assignment
            3. You have to first try getting a match on special MCCs in "special_mcc_dictionary" and then in case there is no match then you have to refer "visa_mcc_data".
            * Tasks End *

            * Input Data Starts *
            1. Website Information as a dictionary containing below keys
                a. "product_services": holistic types covering product and services,
                b. "line_of_business":
                c. "customers":
                d. "website_description":
                {website_info}

            2. special_mcc_data ("HIGH PRIORITY") : This contains
                a. "category":
                b. "description":
                c. "mcc":
                {filtered_special_mcc}

            3. visa_and_master_mcc_data ("LOW PRIORITY") : This contains
                a. "mcc":
                b. "title":
                c. "description":
                d. "included_product_services":
                {filtered_visa_data}

            * Input Data Ends *

            * Guidelines *
                - Focus ONLY on the website information provided to determine the business type
                - Do NOT use any external knowledge or assumptions about the website
                - Do NOT scrape additional data for getting the appropriate MCC
                - Use ONLY the MCC information provided in the input section
                - The special_mcc_data has HIGHER PRIORITY than the visa_and_master_mcc_data, so first try to find a match from special_mcc_data, in case a match is not found then refer visa_and_master_mcc_data.
                - assign mcc as -1 and other keys as null if in case the website is not reachable or not valid, domain is up for sale, redirecting to other domain.

            * Decision Process *
            1. First, identify the PRIMARY business activity from the website information provided
            2. Look for an EXACT match in the special_mcc_dictionary
            3. If no exact match, look for the CLOSEST specific category (avoid general categories)
            4. If still uncertain, check the Visa MCC data for additional context
            5. Choose the most SPECIFIC category that fits (rather than a general one)
            6. If multiple MCCs could apply, choose the one that best matches the PRIMARY business

            * Output Format *
            Provide your answer STRICTLY in this JSON format only 5 keys:
            {{
            "mcc": "str",
            "business_desc": "str", not exceeding 50 words,
            "business_category": "str", not exceeding 50 words,
            "reason": "<clear explanation with direct reference to website info and matching MCC description>", not exceeding 150 words,
            "website": "<URL of the website>",
            }}
        """

        return prompt
    
    @staticmethod
    def get_home_page_url_prompt(website, urls_list):
        """Generate a prompt to identify the home page URL from a list of URLs"""
        return f"""
        * Role * : You are an expert in website analysis and URL classification.

        * Task Starts *
        You will be given a website name and a list of URLs. Your task is to identify which URL is most likely the home page URL.
        * Task Ends *

        * Input Starts *
        Website: {website}
        URLs: {urls_list}
        * Input Ends *

        * Guidelines *
        1. The home page is generally the landing page and usually the shortest URL amongst all URLs.
        2. Home page URLs typically end with just the domain name or have minimal path components.
        3. Look for URLs that are likely to be the main entry point of the website.
        4. Avoid URLs that are clearly for specific pages like contact, about, products, etc.

        * Output Format *
        Return the output strictly in JSON format with one key:
        {{
            "home_page_url": "the most likely home page URL from the list"
        }}
        """




