import json


class GptPromptPicker:
    @staticmethod
    def get_website_information_prompt(website, url):
        """Generate a prompt for extracting website information from a single URL"""

        prompt = f"""
        * Role * : You are an expert in analysing and extracting information from websites. The information you extract will be used to classify this website into an appropriate merchant category code (referred as MCC).

        * Task Starts *
        You will be given a website URL. Your task will be to visit this URL and extract the information such as:
        1. If the website is live and reachable
        2. If the website redirects to another website
        3. Distinct types of products and services this website sells or offers.
        4. Line of business of this website.
        5. Set of customers this website caters to.
        6. A short website description which can be very helpful for categorizing this business to a MCC.
        * Task Ends *

        * Input Starts *
        You are given following things:
        1. Website : {website}
        2. URL to analyze: {url}
        * Input Ends *

        * Guidelines *
        1. Verify the website URL and ensure it's from the provided website domain only.
        2. Check if the website is active and valid. It should NOT be unreachable OR have domain for sale.
        3. Extract all the products, services, their description mentioned in the website to come up with distinct types of products and services and line of business.
        4. Extract the line of business of this website by going through the URL content.
        5. Look at keywords in products and services first and then images for extracting the set of customers this website caters to, e.g. men, women, boys, girls, children, infants, general public, pets such as dogs, cats etc.

        * Things to keep in mind *
        1. Extract information from the website only using the provided URL - do not include if it navigates to another website or domain.
        2. Do not assume any information, if you are not sure about the information, do not include it in the output.
        3. Website's URL and its meaning may be misleading so tread carefully.
        4. Do not use your past knowledge regarding the website, extracted information should STRICTLY reflect realtime status of the website.
        5. While extracting customers refer explicit keywords first for example: men, women, boys, girls, children, infants, general public, pets such as dogs, cats etc and then refer images if keywords are not explicitly mentioned.

        Please note : Do not continue the analysis if the website is not valid or is redirecting to any other different domain name. Simply assign "is_valid_website" as "no" and "website_redirection" as "yes".

        * Output format starts *

        The output should strictly be in a json format with the below 6 keys and do not return text before or after.
        {{
        "is_valid_website" : <str> "yes" or "no", only one amongst yes and no if the site is not active
        "website_redirection": <str> "yes" or "no"
        "product_services": <str>, holistic types covering product and services,
        "line_of_business": <str>,
        "customers": <str>
        "website_description": <str>
        }}
        * Output format ends *
        """

        return prompt

    @staticmethod
    def get_website_mcc_classifier_prompt_full(website_info, filtered_special_mcc, filtered_visa_data, website_url=None):
        
        prompt = f"""
                You are expert in finding issues in wesbite/URL usabilty and an expert in security analysis. 
                I need you to perform a security and usability analysis on the following URL: [Insert URL Here]
                Please check for these three specific things:
                *** Page Navigation Issues ***
                1. Page Navigation Issues: Analyze the page's content and link structure to identify any potential navigation problems or a confusing layout.
                2. If there are some inactive buttons or dropdowns etc. which dont work at all and clickable, which are in general links on other websites, like privacy, about us, terms and conditions etc. 
                3. Redirection to same page" --> If there are some links which gets redirected back to the same page. For this analyze the Link's Destination (href): For each link found, analyze its href attribute to see where it points, specifically look for links that:
                    a. Point to the exact same URL as the page I'm currently on.
                    b. Use a hash symbol (#) as the destination (e.g., href="#"). In web development, this is a standard way to create a link that goes to the top of the current page, effectively reloading or refocusing it.
                    c. Have an empty href attribute (href=""), which also causes a reload of the current page.
                *** Website Securoty Issues***
                1. Phishing Indicators: Assess whether the site has the characteristics of a phishing attempt. Look for suspicious elements in the content, check the URL format, and search for its reputation.
                2. Malware Risk: Investigate if the URL is flagged on any known security blacklists or has a reputation for distributing malware."
                3. If this URL or site is listed anywhere as a security threat.
                4. If anyone has given a review for this site where it indicates that site is not safe and security concerns or the customer has been scammed through this. 

                *** Guidelines ***
                Page navigation issues can be following for example.
                1. Broken Links  --->  Clicking a link leads to a 404 or non-functional page ---> “Contact Us” → 404 Not Found                                              
                2. JavaScript Errors ---> JS-based navigation buttons don’t respond or crash ---> Clicking a dropdown fails silently                                        
                3. Login Walls --->Clicking a link redirects to login or signup ---> “Pricing” → requires login                                                
                4. Infinite Redirect Loops ---> Page keeps redirecting between URLs ---> `/home` → `/main` → `/home`...                                            
                5. Disabled Links ---> Links or buttons appear clickable but do nothing ---> “Terms & Conditions” button does nothing                                  
                6. Non-standard Navigation ---> Website uses dynamic pop-ups/modals instead of clean page loads ---> Navigation inside single-page apps (SPAs) fails without proper wait logic |

                Output should strictly be in below given format as a json with 9 keys.
                {{"navigation_issues_exists": a string, yes/no, 
                "navigation_issue_type": a list [], one amongst mentioned in "Page navigation issues"
                "navigation_issues_area": a list [], a list of buttons, dropdowns, etc. whch are inactive and/or are dummy. 
                "redirection_same_page": a string, yes/no, Yes if there are some links which redirects back to the same page
                "links" : a list [], a list of links which redirect to the same page again  in case they exists.
                "phishing_site" : a string, yes/no, if its a phishing website
                "phishing_reason": a string, short reason if it was classified at phishing site, in 10-15 words.
                "malware_present: a string, yes/no, yes if there is a malware present in it
                "malware_reason":a string, short reason if it was found that a malware was present, in 10-15 words.
                "security_review_present" : a string, yes/no, yes if there a exists a review online where this site has been mentioned for a security related issue
                "security_review": a string, if a security related issue was found, a short description of the issue, in 10-15 words.
                "security_review_source": a list [], a list of platforms where the review has been mentioned.
                }}
        """

        return prompt

    @staticmethod
    def get_mcc_classification_prompt(website_info, website_url=None):
        """Generate MCC classification prompt based on website information"""

        prompt = f"""
        * Role * : You are an expert in Merchant Category Code (MCC) classification. You specialize in analyzing business information and assigning the most appropriate MCC code.

        * Task Starts *
        You will be given website information extracted from a business website. Your task is to:
        1. Analyze the business information provided
        2. Determine the most appropriate MCC code for this business
        3. Provide a clear explanation for your classification decision
        * Task Ends *

        * Input Starts *
        Website URL: {website_url}
        Business Information:
        - Products/Services: {website_info.get('product_services', 'Not specified')}
        - Line of Business: {website_info.get('line_of_business', 'Not specified')}
        - Target Customers: {website_info.get('customers', 'Not specified')}
        - Website Description: {website_info.get('website_description', 'Not specified')}
        * Input Ends *

        * Guidelines *
        1. Choose the MCC code that best represents the primary business activity
        2. If multiple business activities are present, choose the one that appears to be the main revenue source
        3. Consider the target customers and business model when making the decision
        4. Provide a clear, concise explanation for your choice
        5. If the business information is insufficient, indicate this in your response

        * Output Format *
        Return the output strictly in JSON format with the following keys:
        {{
            "mcc": "4-digit MCC code",
            "business_category": "General category name",
            "business_desc": "Brief description of the business",
            "reason": "Explanation for why this MCC was chosen",
            "confidence": "high/medium/low based on available information"
        }}
        """

        return prompt

    @staticmethod
    def get_home_page_url_prompt(website, urls_list):
        """Generate a prompt to identify the home page URL from a list of URLs"""
        return f"""
        * Role * : You are an expert in website analysis and URL classification.

        * Task Starts *
        You will be given a website name and a list of URLs. Your task is to identify which URL is most likely the home page URL.
        * Task Ends *

        * Input Starts *
        Website: {website}
        URLs: {urls_list}
        * Input Ends *

        * Guidelines *
        1. The home page is generally the landing page and usually the shortest URL amongst all URLs.
        2. Home page URLs typically end with just the domain name or have minimal path components.
        3. Look for URLs that are likely to be the main entry point of the website.
        4. Avoid URLs that are clearly for specific pages like contact, about, products, etc.

        * Output Format *
        Return the output strictly in JSON format with one key:
        {{
            "home_page_url": "the most likely home page URL from the list"
        }}
        """




